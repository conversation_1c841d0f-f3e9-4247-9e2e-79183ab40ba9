# Services & Projects Image Relationship Analysis

## Live Website Observations

After examining the live website at `http://localhost:5173`, I've identified the complete image-to-content relationship system.

### Services Page ("Hva vi gjør") Analysis

**Observed Services with Images**:
1. **Planlegging og Design** → No categorized image (uses hero image)
2. **Belegningsstein** → `/images/categorized/belegg/IMG_3037_60.181492_10.274272.webp`
3. **Cortenstål** → `/images/categorized/stål/IMG_3847.webp`
4. **Støttemurer** → `/images/categorized/støttemur/IMG_2855.webp`
5. **Platting** → `/images/categorized/platting/IMG_4188.webp`
6. **Ferdigplen** → `/images/categorized/ferdigplen/IMG_1912.webp`
7. **Kantstein** → `/images/categorized/kantstein/IMG_0716.webp`
8. **Trapper og Repoer** → `/images/categorized/trapp-repo/IMG_4111.webp`
9. **Hek<PERSON> og Beplantning** → `/images/categorized/hekk/IMG_2370.webp`

### Projects Page ("Prosjekter") Analysis

**Observed Projects with Images**:
1. **Moderne Hage på Røyse** (Cortenstål) → `/images/categorized/stål/IMG_3847.webp`
2. **Eksklusiv Terrasse** (Platting) → `/images/categorized/platting/IMG_4188.webp`
3. **Natursteinmur i Hønefoss** (Støttemur) → `/images/categorized/støttemur/IMG_2855.webp`
4. **Innkjørsel med belegningsstein** (Belegningsstein) → `/images/categorized/belegg/IMG_3037_60.181492_10.274272.webp`
5. **Ferdigplen i Vik** (Ferdigplen) → `/images/categorized/ferdigplen/IMG_1912.webp`
6. **Kantstein i Sundvollen** (Kantstein) → `/images/categorized/kantstein/IMG_0716.webp`
7. **Granitttrapp i Jevnaker** (Trapper og Repoer) → `/images/categorized/trapp-repo/IMG_4111.webp`
8. **Hekk og beplantning i Hole** (Hekk og Beplantning) → `/images/categorized/hekk/IMG_2370.webp`

## Image Mapping System Architecture

### 1. Service ID → Image Category Mapping

**Configuration**: `src/lib/config/images.ts`
```typescript
export const SERVICE_TO_IMAGE_CATEGORY = {
  belegningsstein: 'belegg',        // Service: Belegningsstein → Folder: belegg
  cortenstaal: 'stål',             // Service: Cortenstål → Folder: stål  
  stottemurer: 'støttemur',         // Service: Støttemurer → Folder: støttemur
  granitt: 'trapp-repo',            // Service: Trapper og Repoer → Folder: trapp-repo
  kantstein: 'kantstein',           // Service: Kantstein → Folder: kantstein
  platting: 'platting',             // Service: Platting → Folder: platting
  beplantning: 'hekk',              // Service: Hekk og Beplantning → Folder: hekk
  ferdigplen: 'ferdigplen'          // Service: Ferdigplen → Folder: ferdigplen
} as const;
```

### 2. Project Category → Image Category Mapping

**Configuration**: `src/lib/config/images.ts`
```typescript
export const PROJECT_CATEGORY_TO_IMAGE_CATEGORY = {
  'Cortenstål': 'stål',            // Project: Cortenstål → Folder: stål
  'Platting': 'platting',          // Project: Platting → Folder: platting
  'Støttemur': 'støttemur',         // Project: Støttemur → Folder: støttemur
  'Belegningsstein': 'belegg',      // Project: Belegningsstein → Folder: belegg
  'Ferdigplen': 'ferdigplen',       // Project: Ferdigplen → Folder: ferdigplen
  'Kantstein': 'kantstein',         // Project: Kantstein → Folder: kantstein
  'Trapper og Repoer': 'trapp-repo', // Project: Trapper og Repoer → Folder: trapp-repo
  'Hekk og Beplantning': 'hekk'     // Project: Hekk og Beplantning → Folder: hekk
} as const;
```

### 3. Featured Images per Category

**Configuration**: `src/lib/config/images.ts`
```typescript
export const FEATURED_IMAGES = {
  belegg: 'IMG_3037_60.181492_10.274272.webp',    // Geocoordinate filename
  stål: 'IMG_3847.webp',                          // Standard filename
  støttemur: 'IMG_2855.webp',                     // Standard filename
  'trapp-repo': 'IMG_4111.webp',                  // Standard filename
  kantstein: 'IMG_0716.webp',                     // Standard filename
  platting: 'IMG_4188.webp',                      // Standard filename
  hekk: 'IMG_2370.webp',                          // Standard filename
  ferdigplen: 'IMG_1912.webp'                     // Standard filename
} as const;
```

## Resolution Flow Analysis

### Service Image Resolution

**Flow**: Service ID → Image Category → Featured Image → Full Path

```typescript
// Example: "belegningsstein" service
getServiceFeaturedImage('belegningsstein')
  → SERVICE_TO_IMAGE_CATEGORY['belegningsstein'] = 'belegg'
  → getFeaturedImagePath('belegg')
  → FEATURED_IMAGES['belegg'] = 'IMG_3037_60.181492_10.274272.webp'
  → `/images/categorized/belegg/IMG_3037_60.181492_10.274272.webp`
```

### Project Image Resolution

**Flow**: Project Category → Image Category → Featured Image → Full Path

```typescript
// Example: "Cortenstål" project category
getProjectFeaturedImage('Cortenstål')
  → PROJECT_CATEGORY_TO_IMAGE_CATEGORY['Cortenstål'] = 'stål'
  → getFeaturedImagePath('stål')
  → FEATURED_IMAGES['stål'] = 'IMG_3847.webp'
  → `/images/categorized/stål/IMG_3847.webp`
```

## Key Architectural Insights

### 1. **Shared Image Categories**
Services and projects share the same underlying image category system:
- Both "Cortenstål" service and "Cortenstål" project → `stål` folder
- Both "Belegningsstein" service and "Belegningsstein" project → `belegg` folder

### 2. **Featured Image Strategy**
Each category has exactly **one featured image** that represents that category across:
- Service showcase pages
- Project cards
- Category galleries (as the primary image)

### 3. **Geocoordinate Integration**
Some featured images contain GPS coordinates in filenames:
- `IMG_3037_60.181492_10.274272.webp` (latitude: 60.181492, longitude: 10.274272)
- This enables location-based features and metadata extraction

### 4. **Norwegian Character Handling**
Category folders use Norwegian characters (`stål`, `støttemur`) which are URL-encoded via `encodeImagePath()`:
- `stål` → `st%C3%A5l` in URLs
- `støttemur` → `st%C3%B8ttemur` in URLs

## Dynamic Image Loading Implementation

### For Dynamic Image Loader System

**Required Mappings**:
```typescript
interface ImageCollections {
  hero: {
    'home-main': string;
    'about-ringerike': string;
    'services-granite': string;
    'projects-grass': string;
    'contact-illustrative': string;
    'testimonials-cortensteel': string;
  };
  
  team: {
    'kim': string;
    'jan': string;
    'firma': string;
  };
  
  categories: {
    belegg: string[];      // All images in belegg folder
    stål: string[];        // All images in stål folder
    støttemur: string[];   // All images in støttemur folder
    'trapp-repo': string[]; // All images in trapp-repo folder
    kantstein: string[];   // All images in kantstein folder
    platting: string[];    // All images in platting folder
    hekk: string[];        // All images in hekk folder
    ferdigplen: string[];  // All images in ferdigplen folder
  };
  
  featured: {
    belegg: string;        // Featured image for belegg category
    stål: string;          // Featured image for stål category
    støttemur: string;     // Featured image for støttemur category
    // ... etc for all categories
  };
}
```

### Service Integration Points

**Services Page** (`src/sections/30-services/index.tsx`):
- Uses `getServiceFeaturedImage(serviceId)` for each service card
- Needs to be replaced with `ImageService.getCategoryFeatured(category)`

**Projects Page** (`src/sections/40-projects/index.tsx`):
- Uses `getProjectFeaturedImage(category)` for each project card  
- Needs to be replaced with `ImageService.getCategoryFeatured(category)`

**Project Gallery** (`src/sections/40-projects/ProjectGallery.tsx`):
- Uses `getImagesFromCategory(category)` for gallery views
- Needs to be replaced with `ImageService.getCategoryImages(category)`

## Migration Strategy for Dynamic Loader

### Phase 1: Infrastructure
1. Create `import.meta.glob` loaders for each image directory
2. Build type-safe `ImageService` with category mappings
3. Preserve existing `encodeImagePath()` functionality

### Phase 2: Service/Project Integration  
1. Replace `getServiceFeaturedImage()` calls with `ImageService.getCategoryFeatured()`
2. Replace `getProjectFeaturedImage()` calls with `ImageService.getCategoryFeatured()`
3. Update gallery components to use `ImageService.getCategoryImages()`

### Phase 3: Metadata Enhancement
1. Extract geocoordinates from filenames automatically
2. Generate alt-text based on category and location data
3. Add build-time validation for missing images

The current system demonstrates excellent separation of concerns with services and projects sharing a unified image category system, making it ideal for migration to a dynamic image loader.
