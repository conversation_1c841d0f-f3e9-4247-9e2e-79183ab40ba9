import React, { useState, useEffect } from 'react';
import { X, AlertTriangle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { IMAGE_CATEGORIES, GeoImage } from '@/lib/utils/images';
import { encodeImagePath } from '@/lib/utils/paths';
import { ImageService } from '@/lib/services/ImageService';
import { ImageErrorBoundary, useImageErrorHandler } from '@/components/ErrorBoundary/ImageErrorBoundary';

interface ProjectGalleryProps {
  category?: keyof typeof IMAGE_CATEGORIES;
  className?: string;
}

const ProjectGallery: React.FC<ProjectGalleryProps> = ({
  category,
  className
}) => {
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [images, setImages] = useState<GeoImage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);

  const { handleImageError, getFallbackSrc } = useImageErrorHandler();
  const maxRetries = 3;

  // Load images dynamically when category changes
  useEffect(() => {
    const loadImages = async () => {
      if (!category) {
        setImages([]);
        setLoading(false);
        setError(null);
        return;
      }

      setLoading(true);
      setError(null);

      try {
        const galleryImages = await ImageService.getCategoryGalleryImages(category);

        if (galleryImages.length === 0) {
          setError(`Ingen bilder funnet for kategorien "${category}"`);
        } else {
          setImages(galleryImages);
        }
      } catch (error) {
        console.error('Error loading gallery images:', error);
        setError(error instanceof Error ? error.message : 'Ukjent feil ved lasting av bilder');
        setImages([]);
      } finally {
        setLoading(false);
      }
    };

    loadImages();
  }, [category, retryCount]);

  const handleRetry = () => {
    if (retryCount < maxRetries) {
      setRetryCount(prev => prev + 1);
    }
  };

  if (loading) {
    return (
      <div className="text-center py-8">
        <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
        <p className="text-gray-500 mt-2">Laster bilder...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <div className="flex items-center justify-center w-16 h-16 bg-red-100 rounded-full mx-auto mb-4">
          <AlertTriangle className="h-8 w-8 text-red-600" />
        </div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Feil ved lasting av bilder</h3>
        <p className="text-gray-600 mb-4">{error}</p>
        {retryCount < maxRetries && (
          <button
            onClick={handleRetry}
            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
          >
            Prøv igjen ({maxRetries - retryCount} forsøk igjen)
          </button>
        )}
      </div>
    );
  }

  if (!images.length) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">Ingen bilder tilgjengelig for denne kategorien.</p>
      </div>
    );
  }

  return (
    <ImageErrorBoundary fallbackImage={getFallbackSrc('category')}>
      <div className={cn(
        "grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",
        className
      )}>
        {images.map((image, index) => (
          <button
            key={index}
            className="relative aspect-square overflow-hidden rounded-lg cursor-pointer group"
            onClick={() => setSelectedImage(image.path)}
          >
            <img
              src={encodeImagePath(image.path)}
              alt={image.metadata?.title || ''}
              className="absolute inset-0 w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
              onError={() => handleImageError(image.path)}
            />
            <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <div className="absolute bottom-0 left-0 right-0 p-4 text-white">
                <h3 className="text-sm font-medium">
                  {image.metadata?.title}
                </h3>
                {image.metadata?.description && (
                  <p className="text-xs mt-1 text-gray-200">
                    {image.metadata.description}
                  </p>
                )}
              </div>
            </div>
          </button>
        ))}
      </div>

      {/* Modal */}
      {selectedImage && (
        <div
          className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4"
          onClick={() => setSelectedImage(null)}
        >
          <button
            className="absolute top-4 right-4 text-white p-2 hover:bg-white/10 rounded-full"
            onClick={() => setSelectedImage(null)}
          >
            <X className="w-6 h-6" />
          </button>
          <img
            src={encodeImagePath(selectedImage)}
            alt="Project detail"
            className="max-w-full max-h-[90vh] rounded-lg"
            onError={() => {
              console.warn('Modal image failed to load:', selectedImage);
              setSelectedImage(null);
            }}
          />
        </div>
      )}
    </ImageErrorBoundary>
  );
};

export default ProjectGallery;