# Dynamic Image Loader Implementation Plan

## Executive Summary

Based on live website analysis and codebase examination, this plan outlines the **highest-impact changes** to replace all hardcoded image paths with a dynamic `import.meta.glob` system while preserving the sophisticated service-project-category relationship architecture.

## 🎯 Core Implementation Strategy

### 1. **Dynamic Image Loader Infrastructure**

**File**: `src/lib/assets/imageLoader.ts` (NEW)
```typescript
// Eager load critical images (hero, team)
const heroImages = import.meta.glob('/public/images/hero/*.{webp,jpg,png}', { 
  eager: true, 
  as: 'url' 
});

const teamImages = import.meta.glob('/public/images/team/*.{webp,jpg,png}', { 
  eager: true, 
  as: 'url' 
});

// Lazy load category images for performance
const categoryImages = import.meta.glob('/public/images/categorized/**/*.{webp,jpg,png}', { 
  eager: false, 
  as: 'url' 
});

// Process and organize images by category
const processCategoryImages = (globResult: Record<string, any>) => {
  const categories: Record<string, string[]> = {};
  
  Object.entries(globResult).forEach(([path, moduleOrUrl]) => {
    // Extract category from path: /public/images/categorized/belegg/IMG_123.webp
    const match = path.match(/\/categorized\/([^\/]+)\/([^\/]+)$/);
    if (match) {
      const [, category, filename] = match;
      if (!categories[category]) categories[category] = [];
      categories[category].push(typeof moduleOrUrl === 'function' ? moduleOrUrl : moduleOrUrl);
    }
  });
  
  return categories;
};

export const imageCollections = {
  hero: processHeroImages(heroImages),
  team: processTeamImages(teamImages), 
  categories: processCategoryImages(categoryImages)
};
```

### 2. **Type-Safe Image Service**

**File**: `src/lib/services/ImageService.ts` (NEW)
```typescript
import { imageCollections } from '@/lib/assets/imageLoader';
import { encodeImagePath } from '@/lib/utils/paths';

// Preserve existing category mappings
import { 
  SERVICE_TO_IMAGE_CATEGORY,
  PROJECT_CATEGORY_TO_IMAGE_CATEGORY 
} from '@/lib/config/images';

export class ImageService {
  // Hero images with fallbacks
  static getHeroImage(key: string): string {
    const image = imageCollections.hero[key];
    return image ? encodeImagePath(image) : this.getFallbackHero();
  }
  
  // Team images with fallbacks  
  static getTeamImage(memberId: string): string {
    const image = imageCollections.team[memberId];
    return image ? encodeImagePath(image) : this.getFallbackTeam();
  }
  
  // Category featured images (replaces getServiceFeaturedImage & getProjectFeaturedImage)
  static getCategoryFeatured(category: string): string {
    const images = imageCollections.categories[category];
    if (images && images.length > 0) {
      // Use first image as featured (maintains current behavior)
      return encodeImagePath(images[0]);
    }
    return this.getFallbackCategory();
  }
  
  // Service images (preserves existing service-to-category mapping)
  static getServiceImage(serviceId: string): string {
    const category = SERVICE_TO_IMAGE_CATEGORY[serviceId as keyof typeof SERVICE_TO_IMAGE_CATEGORY];
    return category ? this.getCategoryFeatured(category) : this.getFallbackCategory();
  }
  
  // Project images (preserves existing project-to-category mapping)
  static getProjectImage(projectCategory: string): string {
    const category = PROJECT_CATEGORY_TO_IMAGE_CATEGORY[projectCategory as keyof typeof PROJECT_CATEGORY_TO_IMAGE_CATEGORY];
    return category ? this.getCategoryFeatured(category) : this.getFallbackCategory();
  }
  
  // Gallery images for categories
  static getCategoryImages(category: string): string[] {
    const images = imageCollections.categories[category] || [];
    return images.map(img => encodeImagePath(img));
  }
  
  // Fallback methods
  private static getFallbackHero(): string {
    return '/images/hero/hero-home-main.webp'; // Default fallback
  }
  
  private static getFallbackTeam(): string {
    return '/images/team/ringerikelandskap-firma.webp'; // Company image fallback
  }
  
  private static getFallbackCategory(): string {
    return '/images/hero/hero-services-granite.webp'; // Generic fallback
  }
}
```

## 🔧 High-Impact Replacement Points

### 3. **Replace Service Image Resolution**

**File**: `src/data/services.ts`

**Before**:
```typescript
{
  id: "belegningsstein",
  image: getServiceFeaturedImage('belegningsstein'), // HARDCODED FUNCTION
}
```

**After**:
```typescript
{
  id: "belegningsstein", 
  image: ImageService.getServiceImage('belegningsstein'), // DYNAMIC SERVICE
}
```

### 4. **Replace Project Image Resolution**

**File**: `src/data/projects.ts`

**Before**:
```typescript
{
  id: "moderne-hage-royse",
  image: getProjectFeaturedImage('Cortenstål'), // HARDCODED FUNCTION
  category: "Cortenstål",
}
```

**After**:
```typescript
{
  id: "moderne-hage-royse",
  image: ImageService.getProjectImage('Cortenstål'), // DYNAMIC SERVICE
  category: "Cortenstål",
}
```

### 5. **Replace Hero Image References**

**File**: `src/sections/10-home/index.tsx`

**Before**:
```typescript
<Hero backgroundImage="/images/hero/hero-home-main.webp" />
```

**After**:
```typescript
<Hero backgroundImage={ImageService.getHeroImage('home-main')} />
```

**File**: `src/lib/constants/page-content.ts`

**Before**:
```typescript
export const ABOUT_PAGE = {
  meta: {
    image: '/images/hero/hero-about-ringerike.webp',
  }
};
```

**After**:
```typescript
export const ABOUT_PAGE = {
  meta: {
    image: ImageService.getHeroImage('about-ringerike'),
  }
};
```

### 6. **Replace Team Image System**

**File**: `src/lib/constants/contact.ts`

**Before**:
```typescript
team: [
  {
    name: 'Kim Tuvsjøen',
    image: '/images/team/ringerikelandskap-kim.webp', // HARDCODED
  }
]
```

**After**:
```typescript
team: [
  {
    name: 'Kim Tuvsjøen',
    image: ImageService.getTeamImage('kim'), // DYNAMIC
  }
]
```

### 7. **Replace Gallery Image Loading**

**File**: `src/sections/40-projects/ProjectGallery.tsx`

**Before**:
```typescript
const images = category ? getImagesFromCategory(category) : [];
```

**After**:
```typescript
const images = category ? ImageService.getCategoryImages(category) : [];
```

## 📋 Build Configuration Changes

### 8. **Update Vite Configuration**

**File**: `vite.config.ts`
```typescript
export default defineConfig({
  // Keep publicDir for non-imported assets (favicons, etc.)
  publicDir: "public", 
  
  build: {
    rollupOptions: {
      output: {
        // Organize imported images
        assetFileNames: (assetInfo) => {
          if (assetInfo.name?.match(/\.(png|jpe?g|webp|svg)$/)) {
            return 'assets/images/[name]-[hash][extname]';
          }
          return 'assets/[name]-[hash][extname]';
        }
      }
    }
  }
});
```

## 🎯 Metadata Preservation Strategy

### 9. **Enhanced Image Metadata**

**File**: `src/lib/assets/imageMetadata.ts` (NEW)
```typescript
interface ImageMetadata {
  alt: string;
  title?: string;
  description?: string;
  coordinates?: { lat: number; lng: number };
}

// Auto-generate metadata from existing patterns
export const generateImageMetadata = (filename: string, category: string): ImageMetadata => {
  // Extract geocoordinates if present
  const geoMatch = filename.match(/_(\d+\.\d+)_(\d+\.\d+)\./);
  const coordinates = geoMatch ? {
    lat: parseFloat(geoMatch[1]),
    lng: parseFloat(geoMatch[2])
  } : undefined;
  
  // Generate contextual alt text
  const categoryNames = {
    'belegg': 'Belegningsstein',
    'stål': 'Cortenstål',
    'støttemur': 'Støttemur',
    // ... all categories
  };
  
  return {
    alt: `${categoryNames[category] || category} prosjekt${coordinates ? ` (${coordinates.lat}, ${coordinates.lng})` : ''}`,
    title: `Profesjonell ${categoryNames[category]?.toLowerCase()} utførelse`,
    coordinates
  };
};
```

## 🚀 Migration Timeline

### **Phase 1: Core Infrastructure** (Day 1-2)
1. ✅ Create `imageLoader.ts` with `import.meta.glob`
2. ✅ Build `ImageService` class with all methods
3. ✅ Add metadata generation system

### **Phase 2: Replace Dynamic References** (Day 3-4)  
4. ✅ Replace `getServiceFeaturedImage()` → `ImageService.getServiceImage()`
5. ✅ Replace `getProjectFeaturedImage()` → `ImageService.getProjectImage()`
6. ✅ Replace gallery loading → `ImageService.getCategoryImages()`

### **Phase 3: Replace Hardcoded References** (Day 5-6)
7. ✅ Replace all hero image hardcoded paths
8. ✅ Replace team image system in `CONTACT_INFO`
9. ✅ Replace company image in AboutPage

### **Phase 4: Validation & Testing** (Day 7)
10. ✅ Add build-time image validation
11. ✅ Test all pages for missing images
12. ✅ Verify Norwegian character encoding still works

## 📊 Expected Benefits

- **🚀 Performance**: Vite's optimized asset handling + lazy loading for categories
- **🛡️ Type Safety**: Compile-time validation of all image references
- **🔧 Maintainability**: Zero hardcoded paths, automatic discovery
- **📱 Scalability**: Add new images without code changes
- **🎯 SEO**: Enhanced metadata with geocoordinate extraction
- **⚡ DX**: Auto-completion and build-time validation

## 🔄 Backward Compatibility

During migration, maintain compatibility:
```typescript
// Temporary bridge functions
export const getServiceFeaturedImage = (serviceId: string): string => {
  console.warn('getServiceFeaturedImage is deprecated, use ImageService.getServiceImage');
  return ImageService.getServiceImage(serviceId);
};

export const getProjectFeaturedImage = (category: string): string => {
  console.warn('getProjectFeaturedImage is deprecated, use ImageService.getProjectImage');
  return ImageService.getProjectImage(category);
};
```

This implementation preserves the sophisticated service-project-category relationship system while modernizing the image loading pipeline with type safety and automatic discovery.
