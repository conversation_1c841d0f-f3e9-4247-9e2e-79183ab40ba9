/**
 * Dynamic Image Loader using import.meta.glob
 * 
 * This module uses Vite's import.meta.glob to dynamically load all images
 * at build time, providing type-safe access to image assets while maintaining
 * the existing Norwegian character support and category organization.
 */

// Eager load critical images for immediate availability
//
// IMPORTANT: Vite Public Directory Handling
// =========================================
// We use /public/ prefix in import.meta.glob patterns because:
// 1. This is the only way to access public directory assets with glob
// 2. Vite will show warnings in dev mode, but this is expected behavior
// 3. We strip the /public prefix from URLs since Vite serves public assets from root
// 4. This approach works correctly in both development and production builds
//
// The dev server warnings are informational only and don't affect functionality.
const heroImages = import.meta.glob('/public/images/hero/*.{webp,jpg,png}', {
  eager: true,
  query: '?url',
  import: 'default'
});

const teamImages = import.meta.glob('/public/images/team/*.{webp,jpg,png}', {
  eager: true,
  query: '?url',
  import: 'default'
});

// Lazy load category images for performance optimization
const categoryImages = import.meta.glob('/public/images/categorized/**/*.{webp,jpg,png}', {
  eager: false,
  query: '?url',
  import: 'default'
});

/**
 * Process hero images into a clean key-value map
 * Extracts meaningful keys from file paths
 */
const processHeroImages = (globResult: Record<string, unknown>): Record<string, string> => {
  const processed: Record<string, string> = {};

  Object.entries(globResult).forEach(([path, url]) => {
    // Extract filename without extension: /public/images/hero/hero-home-main.webp -> hero-home-main
    const match = path.match(/\/hero\/(.+)\.(webp|jpg|png)$/);
    if (match) {
      const [, filename] = match;
      // Convert hero-home-main to home-main for cleaner API
      const key = filename.replace(/^hero-/, '');
      // Strip /public prefix from URL since Vite serves public assets from root
      processed[key] = (url as string).replace(/^\/public/, '');
    }
  });

  return processed;
};

/**
 * Process team images into a clean key-value map
 * Extracts member IDs from file paths
 */
const processTeamImages = (globResult: Record<string, unknown>): Record<string, string> => {
  const processed: Record<string, string> = {};

  Object.entries(globResult).forEach(([path, url]) => {
    // Extract filename: /public/images/team/ringerikelandskap-kim.webp -> kim
    const match = path.match(/\/team\/ringerikelandskap-(.+)\.(webp|jpg|png)$/);
    if (match) {
      const [, memberId] = match;
      // Strip /public prefix from URL since Vite serves public assets from root
      processed[memberId] = (url as string).replace(/^\/public/, '');
    } else {
      // Handle special cases like firma.webp
      const filenameMatch = path.match(/\/team\/(.+)\.(webp|jpg|png)$/);
      if (filenameMatch) {
        const [, filename] = filenameMatch;
        // Remove ringerikelandskap- prefix if present
        const key = filename.replace(/^ringerikelandskap-/, '');
        // Strip /public prefix from URL since Vite serves public assets from root
        processed[key] = (url as string).replace(/^\/public/, '');
      }
    }
  });

  return processed;
};

/**
 * Process category images into organized collections
 * Handles Norwegian characters and geocoordinate filenames
 */
const processCategoryImages = (globResult: Record<string, () => Promise<unknown>>): Record<string, () => Promise<string[]>> => {
  const categories: Record<string, (() => Promise<unknown>)[]> = {};

  Object.entries(globResult).forEach(([path, moduleLoader]) => {
    // Extract category from path: /public/images/categorized/belegg/IMG_123.webp -> belegg
    const match = path.match(/\/categorized\/([^\/]+)\/([^\/]+)$/);
    if (match) {
      const [, category] = match;
      if (!categories[category]) {
        categories[category] = [];
      }
      categories[category].push(moduleLoader);
    }
  });

  // Convert to functions that return arrays of URLs
  const processed: Record<string, () => Promise<string[]>> = {};
  Object.entries(categories).forEach(([category, loaders]) => {
    processed[category] = async () => {
      const urls = await Promise.all(loaders.map(loader => loader()));
      // Strip /public prefix from URLs since Vite serves public assets from root
      return urls.map(url => (url as string).replace(/^\/public/, ''));
    };
  });
  
  return processed;
};

/**
 * Main image collections object
 * Provides organized access to all image assets
 */
export const imageCollections = {
  hero: processHeroImages(heroImages),
  team: processTeamImages(teamImages),
  categories: processCategoryImages(categoryImages)
};



/**
 * Type definitions for image collections
 */
export interface ImageCollections {
  hero: Record<string, string>;
  team: Record<string, string>;
  categories: Record<string, () => Promise<string[]>>;
}

/**
 * Get available hero image keys
 */
export const getAvailableHeroKeys = (): string[] => {
  return Object.keys(imageCollections.hero);
};

/**
 * Get available team member keys
 */
export const getAvailableTeamKeys = (): string[] => {
  return Object.keys(imageCollections.team);
};

/**
 * Get available category keys
 */
export const getAvailableCategoryKeys = (): string[] => {
  return Object.keys(imageCollections.categories);
};

/**
 * Debug function to log all discovered images
 * Useful for development and troubleshooting
 */
export const debugImageCollections = (): void => {
  console.group('🖼️ Dynamic Image Loader - Discovered Images');

  // Debug raw glob results first
  console.group('Raw Glob Results');
  console.log('Hero glob result:', heroImages);
  console.log('Team glob result:', teamImages);
  console.log('Category glob result keys:', Object.keys(categoryImages));
  console.groupEnd();

  console.group('Hero Images');
  Object.entries(imageCollections.hero).forEach(([key, url]) => {
    console.log(`${key}: ${url}`);
  });
  console.groupEnd();

  console.group('Team Images');
  Object.entries(imageCollections.team).forEach(([key, url]) => {
    console.log(`${key}: ${url}`);
  });
  console.groupEnd();

  console.group('Category Keys');
  Object.keys(imageCollections.categories).forEach(category => {
    console.log(`${category}: Available`);
  });
  console.groupEnd();

  console.groupEnd();
};
