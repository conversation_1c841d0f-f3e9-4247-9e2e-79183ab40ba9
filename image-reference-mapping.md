# Complete Image Reference Mapping

## Hard-coded Image Folders & Files

### `/public/images/categorized/` - Project Category Images
```
belegg/          - Paving stone projects (35+ images)
ferdigplen/      - Ready-made lawn projects (2 images)  
hekk/            - Hedge and planting projects (8+ images)
kantstein/       - Curb stone projects (4+ images)
platting/        - Paving projects (2 images)
stål/            - Corten steel projects (15+ images)
støttemur/       - Retaining wall projects (8+ images)
trapp-repo/      - Stairs and repositories (4+ images)
```

### `/public/images/hero/` - Hero Section Backgrounds
```
hero-about-ringerike.webp      - About page hero
hero-contact-illustrative.webp - Contact page hero
hero-home-main.webp           - Homepage main hero
hero-projects-grass.webp      - Projects page hero
hero-projects-showcase.webp   - Projects showcase hero
hero-services-granite.webp    - Services page hero (granite)
hero-services-grass.webp      - Services page hero (grass)
hero-testimonials-cortensteel.webp - Testimonials hero
```

### `/public/images/team/` - Team Member Photos
```
ringerikelandskap-firma.webp  - Company/team photo
ringerikelandskap-jan.webp    - <PERSON>'s profile photo
ringerikelandskap-kim.webp    - <PERSON>'s profile photo
```

### `/public/images/icons/` - Icon Assets
```
dummy-icon.svg      - Placeholder icon
google-icon.svg     - Google reviews icon
mittanbud-icon.svg  - MittAnbud platform icon
```

### `/public/logos/` - Company Logos
```
base.png           - Base logo PNG
base.svg           - Base logo SVG
text.svg           - Text-only logo
layout1/           - Logo layout variant 1
layout2/           - Logo layout variant 2
layout3/           - Logo layout variant 3
```

## Code Locations That Import/Embed Images

### 1. Central Configuration Files

**`src/lib/config/images.ts`** - Master image configuration
```typescript
export const IMAGE_PATHS = {
  hero: {
    main: '/images/hero/hero-home-main.webp',
    grass: '/images/hero/hero-projects-grass.webp',
    // ... all hero images
  },
  team: {
    kim: '/images/team/ringerikelandskap-kim.webp',
    jan: '/images/team/ringerikelandskap-jan.webp',
    firma: '/images/team/ringerikelandskap-firma.webp'
  },
  icons: {
    mittAnbud: '/images/icons/mittanbud-icon.svg',
    google: '/images/icons/google-icon.svg'
  },
  categories: {
    belegg: '/images/categorized/belegg',
    hekk: '/images/categorized/hekk',
    // ... all category paths
  }
}

export const FEATURED_IMAGES = {
  belegg: 'IMG_3037_60.181492_10.274272.webp',
  stål: 'IMG_3847.webp',
  støttemur: 'IMG_2855.webp',
  // ... featured image for each category
}
```

**`src/lib/utils/images.ts`** - Image processing utilities
```typescript
const FALLBACK_IMAGES: Record<string, string[]> = {
  belegg: [
    'IMG_0035_59.923192_10.649406.webp',
    'IMG_0085_60.163047_10.252728.webp',
    // ... 35+ fallback images for belegg category
  ],
  stål: [
    'IMG_3847.webp',
    'IMG_3848.webp',
    // ... 15+ fallback images for steel category
  ]
  // ... fallback arrays for all categories
}
```

### 2. Data Files

**`src/data/testimonials.ts`** - Testimonial source icons
```typescript
import { IMAGE_PATHS } from "@/lib/config/images";

export const testimonials: TestimonialType[] = [
  {
    sourceIcon: IMAGE_PATHS.icons.mittAnbud,
    // ... testimonial data
  }
]
```

**`src/data/projects.ts`** - Project image assignments
```typescript
export const recentProjects: ProjectType[] = [
  {
    image: getProjectFeaturedImage('Cortenstål'),
    // ... project data
  }
]
```

### 3. UI Components

**`src/ui/Hero/index.tsx`** - Hero background images
```typescript
const Hero: React.FC<HeroProps> = ({
  backgroundImage = "/images/hero/hero-home-main.webp",
  // ...
}) => {
  return (
    <div
      style={{
        backgroundImage: `url(${encodeImagePath(backgroundImage)})`,
      }}
    />
  )
}
```

**`src/ui/Logo/index.tsx`** - Inline SVG logo (no external files)
```typescript
// Contains inline SVG paths for logo variants
// No external image dependencies
```

### 4. Section Components

**`src/sections/10-home/index.tsx`** - Homepage hero
```typescript
// Uses IMAGE_PATHS.hero.main via Hero component
```

**`src/sections/20-about/index.tsx`** - Team photos
```typescript
{teamMembers.map((member) => (
  <img
    src={member.image} // References IMAGE_PATHS.team.*
    alt={member.name}
    loading="lazy"
  />
))}
```

**`src/sections/30-services/index.tsx`** - Service images
```typescript
<img
  src={encodeImagePath(service.image)}
  alt={service.title}
  loading="lazy"
/>
```

**`src/sections/40-projects/ProjectCard.tsx`** - Project images
```typescript
<div
  style={{
    backgroundImage: `url(${encodeImagePath(project.image)})`,
  }}
/>
```

**`src/sections/40-projects/ProjectGallery.tsx`** - Category galleries
```typescript
const images = category ? getImagesFromCategory(category) : [];

{images.map((image) => (
  <img
    src={encodeImagePath(image.path)}
    alt={image.metadata?.title}
  />
))}
```

### 5. Helper Functions That Generate Image Paths

**`getFeaturedImagePath(category)`** - Category featured images
**`getServiceFeaturedImage(serviceId)`** - Service-specific images  
**`getProjectFeaturedImage(category)`** - Project category images
**`getImagesFromCategory(category)`** - All images in category
**`encodeImagePath(path)`** - URL encoding for special characters

## Image Loading Patterns

### Static References
- Hero images: Direct path references in configuration
- Team photos: Static paths in team data
- Icons: Static SVG references

### Dynamic Loading
- Project images: Generated via category mapping functions
- Service images: Mapped from service ID to image category
- Gallery images: Loaded from category directories

### Fallback Mechanisms
- Centralized fallback image arrays
- Geocoordinate filename parsing
- WebP format conversion
- Error boundary handling

## Build Tool Integration

### Vite Asset Handling
- `publicDir: "public"` copies all assets to `dist/`
- No image optimization during build
- Direct file serving without processing
- Manual asset chunking for performance

### Path Resolution
- Absolute paths from root (`/images/...`)
- No import-based asset handling
- Runtime path encoding for special characters
- Environment-specific base URL configuration
