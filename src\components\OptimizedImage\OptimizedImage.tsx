import React, { useState, useEffect, useRef, useCallback } from 'react';
import { ImageCacheService } from '@/lib/services/ImageCacheService';
import { ImagePerformanceService } from '@/lib/services/ImagePerformanceService';
import { ImageValidationService } from '@/lib/services/ImageValidationService';

export interface OptimizedImageProps extends Omit<React.ImgHTMLAttributes<HTMLImageElement>, 'loading' | 'onError'> {
  src: string;
  alt: string;
  priority?: 'high' | 'low' | 'auto';
  loading?: 'eager' | 'lazy' | 'auto';
  fallbackSrc?: string;
  onLoadStart?: () => void;
  onLoadComplete?: (loadTime: number) => void;
  onError?: (error: Error) => void;
  enableCache?: boolean;
  enablePerformanceTracking?: boolean;
  placeholder?: React.ReactNode;
  errorFallback?: React.ReactNode;
}

/**
 * High-performance optimized image component with caching and monitoring
 */
export const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  priority = 'auto',
  loading = 'auto',
  fallbackSrc,
  onLoadStart,
  onLoadComplete,
  onError,
  enableCache = true,
  enablePerformanceTracking = true,
  placeholder,
  errorFallback,
  className,
  style,
  ...props
}) => {
  const [imageState, setImageState] = useState<'loading' | 'loaded' | 'error'>('loading');
  const [currentSrc, setCurrentSrc] = useState<string>(src);
  const [loadStartTime, setLoadStartTime] = useState<number>(0);
  const imgRef = useRef<HTMLImageElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  // Determine optimal loading strategy
  const getLoadingStrategy = useCallback(() => {
    if (loading !== 'auto') return loading;
    
    // Auto-determine based on priority and position
    if (priority === 'high') return 'eager';
    if (priority === 'low') return 'lazy';
    
    // Auto priority: check if image is likely above the fold
    return 'lazy'; // Default to lazy for performance
  }, [loading, priority]);

  // Handle image loading with performance tracking
  const handleImageLoad = useCallback(() => {
    const loadTime = performance.now() - loadStartTime;
    
    if (enablePerformanceTracking) {
      const fromCache = ImageCacheService.isCached(currentSrc);
      ImagePerformanceService.trackImageLoad(currentSrc, loadTime, fromCache);
    }
    
    setImageState('loaded');
    onLoadComplete?.(loadTime);
  }, [currentSrc, loadStartTime, enablePerformanceTracking, onLoadComplete]);

  // Handle image error with fallback
  const handleImageError = useCallback(() => {
    const error = new Error(`Failed to load image: ${currentSrc}`);
    
    if (enablePerformanceTracking) {
      ImagePerformanceService.trackImageError(currentSrc, error);
    }

    // Try fallback image if available and not already using it
    if (fallbackSrc && currentSrc !== fallbackSrc) {
      console.warn(`Image load failed, trying fallback: ${currentSrc} -> ${fallbackSrc}`);
      setCurrentSrc(fallbackSrc);
      return;
    }

    // Try service fallback
    if (currentSrc !== ImageValidationService.getFallbackImage()) {
      const serviceFallback = ImageValidationService.getFallbackImage();
      console.warn(`Image load failed, trying service fallback: ${currentSrc} -> ${serviceFallback}`);
      setCurrentSrc(serviceFallback);
      return;
    }

    // All fallbacks failed
    setImageState('error');
    onError?.(error);
  }, [currentSrc, fallbackSrc, enablePerformanceTracking, onError]);

  // Handle image load start
  const handleImageLoadStart = useCallback(() => {
    setLoadStartTime(performance.now());
    setImageState('loading');
    onLoadStart?.();
  }, [onLoadStart]);

  // Preload image if caching is enabled
  useEffect(() => {
    if (enableCache && getLoadingStrategy() === 'eager') {
      ImageCacheService.preloadImage(src, { 
        priority: priority === 'high' ? 'high' : 'low' 
      }).catch(error => {
        console.debug('Preload failed:', error);
      });
    }
  }, [src, enableCache, priority, getLoadingStrategy]);

  // Set up intersection observer for lazy loading
  useEffect(() => {
    if (getLoadingStrategy() === 'lazy' && imgRef.current) {
      observerRef.current = new IntersectionObserver(
        (entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting && imgRef.current) {
              // Start loading when image enters viewport
              imgRef.current.src = currentSrc;
              observerRef.current?.unobserve(imgRef.current);
            }
          });
        },
        {
          rootMargin: '50px 0px', // Start loading 50px before entering viewport
          threshold: 0.1
        }
      );

      observerRef.current.observe(imgRef.current);

      return () => {
        if (observerRef.current && imgRef.current) {
          observerRef.current.unobserve(imgRef.current);
        }
      };
    }
  }, [currentSrc, getLoadingStrategy]);

  // Track image visibility for performance monitoring
  useEffect(() => {
    if (enablePerformanceTracking && imgRef.current && imageState === 'loaded') {
      ImagePerformanceService.observeImage(imgRef.current);
      
      return () => {
        if (imgRef.current) {
          ImagePerformanceService.unobserveImage(imgRef.current);
        }
      };
    }
  }, [imageState, enablePerformanceTracking]);

  // Update src when prop changes
  useEffect(() => {
    setCurrentSrc(src);
    setImageState('loading');
  }, [src]);

  // Render loading placeholder
  if (imageState === 'loading' && placeholder) {
    return <div className={className} style={style}>{placeholder}</div>;
  }

  // Render error fallback
  if (imageState === 'error' && errorFallback) {
    return <div className={className} style={style}>{errorFallback}</div>;
  }

  // Render optimized image
  return (
    <img
      ref={imgRef}
      src={getLoadingStrategy() === 'eager' ? currentSrc : undefined}
      alt={alt}
      className={className}
      style={style}
      loading={getLoadingStrategy()}
      onLoadStart={handleImageLoadStart}
      onLoad={handleImageLoad}
      onError={handleImageError}
      {...props}
    />
  );
};

/**
 * Hook for optimized image loading with performance tracking
 */
export const useOptimizedImage = (src: string, options: {
  enableCache?: boolean;
  enablePerformanceTracking?: boolean;
  priority?: 'high' | 'low';
} = {}) => {
  const [imageState, setImageState] = useState<'loading' | 'loaded' | 'error'>('loading');
  const [loadTime, setLoadTime] = useState<number>(0);

  useEffect(() => {
    const loadImage = async () => {
      const startTime = performance.now();
      setImageState('loading');

      try {
        if (options.enableCache) {
          await ImageCacheService.preloadImage(src, {
            priority: options.priority || 'low'
          });
        }

        const endTime = performance.now();
        const duration = endTime - startTime;
        
        setLoadTime(duration);
        setImageState('loaded');

        if (options.enablePerformanceTracking) {
          ImagePerformanceService.trackImageLoad(src, duration, ImageCacheService.isCached(src));
        }
      } catch (error) {
        setImageState('error');
        
        if (options.enablePerformanceTracking) {
          ImagePerformanceService.trackImageError(src, error as Error);
        }
      }
    };

    loadImage();
  }, [src, options.enableCache, options.enablePerformanceTracking, options.priority]);

  return {
    imageState,
    loadTime,
    isLoading: imageState === 'loading',
    isLoaded: imageState === 'loaded',
    hasError: imageState === 'error'
  };
};

export default OptimizedImage;
