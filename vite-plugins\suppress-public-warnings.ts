/**
 * Vite plugin to suppress public directory warnings for import.meta.glob
 * 
 * This plugin filters out the repetitive warnings about public directory assets
 * that are generated when using import.meta.glob with /public/ prefix.
 * 
 * The warnings are informational and don't affect functionality, but they
 * create noise in the development console.
 */

import type { Plugin } from 'vite';

export function suppressPublicWarnings(): Plugin {
  return {
    name: 'suppress-public-warnings',
    configResolved(config) {
      // Store original logger
      const originalWarn = config.logger.warn;
      
      // Override warn method to filter out public directory warnings
      config.logger.warn = (msg: string, options?: any) => {
        // Skip public directory warnings for import.meta.glob
        if (typeof msg === 'string' && 
            msg.includes('Assets in the public directory are served at the root path')) {
          return;
        }
        
        // Pass through all other warnings
        originalWarn(msg, options);
      };
    }
  };
}
