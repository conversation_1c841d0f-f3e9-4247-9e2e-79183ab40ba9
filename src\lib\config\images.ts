export const IMAGE_PATHS = {
  hero: {
    main: '/images/hero/hero-home-main.webp',
    grass: '/images/hero/hero-projects-grass.webp',
    grass2: '/images/hero/hero-services-grass.webp',
    granite: '/images/hero/hero-services-granite.webp',
    ringerike: '/images/hero/hero-about-ringerike.webp',
    prosjekter: '/images/hero/hero-projects-showcase.webp',
    cortenSteel: '/images/hero/hero-testimonials-cortensteel.webp',
    illustrative: '/images/hero/hero-contact-illustrative.webp'
  },
  team: {
    kim: '/images/team/ringerikelandskap-kim.webp',
    jan: '/images/team/ringerikelandskap-jan.webp',
    firma: '/images/team/ringerikelandskap-firma.webp'
  },
  icons: {
    mittAnbud: '/images/icons/mittanbud-icon.svg',
    google: '/images/icons/google-icon.svg'
  },
  categories: {
    belegg: '/images/categorized/belegg',
    hekk: '/images/categorized/hekk',
    stål: '/images/categorized/stål',
    støttemur: '/images/categorized/støttemur',
    'trapp-repo': '/images/categorized/trapp-repo',
    kantstein: '/images/categorized/kantstein',
    ferdigplen: '/images/categorized/ferdigplen',
    platting: '/images/categorized/platting'
  }
} as const;

export const IMAGE_CATEGORIES = {
  belegg: "Belegningsstein",
  hekk: "Hekk og Beplantning",
  stål: "Cortenstål",
  støttemur: "Støttemurer",
  "trapp-repo": "Trapper og Repoer",
  kantstein: "Kantstein",
  ferdigplen: "Ferdigplen",
  platting: "Platting"
} as const;

// Unified mapping from service IDs to image category folders
// This replaces SERVICE_IMAGE_CATEGORIES from constants/data.ts
export const SERVICE_TO_IMAGE_CATEGORY = {
  belegningsstein: 'belegg',
  cortenstaal: 'stål',
  stottemurer: 'støttemur',
  granitt: 'trapp-repo',
  kantstein: 'kantstein',
  platting: 'platting',
  beplantning: 'hekk',
  ferdigplen: 'ferdigplen'
} as const;

// Centralized featured images for each category
// This eliminates hardcoded image paths throughout the codebase
// Note: Some images have geocoordinates in their filenames
export const FEATURED_IMAGES = {
  belegg: 'IMG_3037_60.181492_10.274272.webp',
  stål: 'IMG_3847.webp',
  støttemur: 'IMG_2855.webp',
  'trapp-repo': 'IMG_4111.webp',
  kantstein: 'IMG_0716.webp',
  platting: 'IMG_4188.webp',
  hekk: 'IMG_2370.webp',
  ferdigplen: 'IMG_1912.webp'
} as const;

// Helper function to get featured image for a category
export const getFeaturedImagePath = (category: keyof typeof FEATURED_IMAGES): string => {
  const filename = FEATURED_IMAGES[category];
  return `/images/categorized/${category}/${filename}`;
};

// Helper function to get featured image for a service ID
// @deprecated Use ImageService.getServiceImage() instead
export const getServiceFeaturedImage = (serviceId: keyof typeof SERVICE_TO_IMAGE_CATEGORY): string => {
  console.warn('getServiceFeaturedImage is deprecated, use ImageService.getServiceImage() instead');
  const category = SERVICE_TO_IMAGE_CATEGORY[serviceId];
  return getFeaturedImagePath(category);
};

// Project category to image category mapping
// This maps project categories to their corresponding image folders
export const PROJECT_CATEGORY_TO_IMAGE_CATEGORY = {
  'Cortenstål': 'stål',
  'Platting': 'platting',
  'Støttemur': 'støttemur',
  'Belegningsstein': 'belegg',
  'Ferdigplen': 'ferdigplen',
  'Kantstein': 'kantstein',
  'Trapper og Repoer': 'trapp-repo',
  'Hekk og Beplantning': 'hekk'
} as const;

// Helper function to get featured image for a project category
// @deprecated Use ImageService.getProjectImage() instead
export const getProjectFeaturedImage = (category: keyof typeof PROJECT_CATEGORY_TO_IMAGE_CATEGORY): string => {
  console.warn('getProjectFeaturedImage is deprecated, use ImageService.getProjectImage() instead');
  const imageCategory = PROJECT_CATEGORY_TO_IMAGE_CATEGORY[category];
  return getFeaturedImagePath(imageCategory);
};
