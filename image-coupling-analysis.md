# Image Coupling Analysis & Dependency Mapping

## Overview

This analysis maps every coupling point where images are referenced in the codebase, categorizing them by coupling strength and identifying potential refactoring opportunities.

## Coupling Categories

### 🔴 STRONG COUPLING - Hardcoded References

#### 1. Hero Images in Page Components

**Location**: `src/sections/10-home/index.tsx`
```typescript
<Hero backgroundImage="/images/hero/hero-home-main.webp" />
```
**Coupling**: Direct string literal in component
**Risk**: High - Changes require code modification

**Location**: `src/lib/constants/page-content.ts`
```typescript
export const ABOUT_PAGE = {
  meta: {
    image: '/images/hero/hero-about-ringerike.webp', // HARDCODED
  }
};
```
**Coupling**: Configuration constant
**Risk**: Medium - Centralized but still hardcoded

#### 2. Team Images in Contact Configuration

**Location**: `src/lib/constants/contact.ts`
```typescript
export const CONTACT_INFO = {
  team: [
    {
      name: '<PERSON>',
      image: '/images/team/ringerikelandskap-kim.webp', // HARDCODED
    },
    {
      name: '<PERSON>',
      image: '/images/team/ringerikelandskap-jan.webp', // HARDCODED
    }
  ]
};
```
**Coupling**: Direct path strings in configuration
**Risk**: Medium - Centralized but brittle

#### 3. Company Image in AboutPage

**Location**: `src/sections/20-about/index.tsx`
```typescript
<img src="/images/team/ringerikelandskap-firma.webp" />
```
**Coupling**: Direct string literal in component
**Risk**: High - Completely hardcoded

#### 4. Featured Images Configuration

**Location**: `src/lib/config/images.ts`
```typescript
export const FEATURED_IMAGES = {
  belegg: 'IMG_3037_60.181492_10.274272.webp', // HARDCODED FILENAME
  stål: 'IMG_3847.webp',                        // HARDCODED FILENAME
  støttemur: 'IMG_2855.webp',                   // HARDCODED FILENAME
  // ... all category featured images
} as const;
```
**Coupling**: Hardcoded filenames with geocoordinates
**Risk**: High - Filename changes break system

### 🟡 MEDIUM COUPLING - Configuration-Based

#### 1. Hero Image Paths

**Location**: `src/lib/config/images.ts`
```typescript
export const IMAGE_PATHS = {
  hero: {
    main: '/images/hero/hero-home-main.webp',
    grass: '/images/hero/hero-projects-grass.webp',
    granite: '/images/hero/hero-services-granite.webp',
    // ... all hero paths
  }
};
```
**Coupling**: Centralized configuration
**Risk**: Medium - Single point of change but still hardcoded

#### 2. Icon References

**Location**: `src/data/testimonials.ts`
```typescript
import { IMAGE_PATHS } from "@/lib/config/images";

export const testimonials: TestimonialType[] = [
  {
    sourceIcon: IMAGE_PATHS.icons.mittAnbud, // CONFIGURATION REFERENCE
  }
];
```
**Coupling**: Configuration dependency
**Risk**: Low - Uses centralized configuration

### 🟢 LOOSE COUPLING - Dynamic Resolution

#### 1. Project Images via Category Mapping

**Location**: `src/data/projects.ts`
```typescript
export const recentProjects: ProjectType[] = [
  {
    image: getProjectFeaturedImage('Cortenstål'), // DYNAMIC FUNCTION
    category: "Cortenstål",
  }
];
```
**Resolution Chain**:
1. `getProjectFeaturedImage('Cortenstål')`
2. → `PROJECT_CATEGORY_TO_IMAGE_CATEGORY['Cortenstål']` = `'stål'`
3. → `getFeaturedImagePath('stål')`
4. → `/images/categorized/stål/IMG_3847.webp`

**Coupling**: Function-based with mapping
**Risk**: Low - Flexible and maintainable

#### 2. Service Images via Service-to-Category Mapping

**Location**: `src/data/services.ts`
```typescript
import { getServiceFeaturedImage } from '@/lib/config/images';

// Service images resolved dynamically
const serviceImage = getServiceFeaturedImage('belegningsstein');
// → 'belegningsstein' maps to 'belegg' category
// → Returns '/images/categorized/belegg/IMG_3037_60.181492_10.274272.webp'
```

**Coupling**: Service ID to category mapping
**Risk**: Low - Abstracted through mapping functions

#### 3. Gallery Images via Category Functions

**Location**: `src/sections/40-projects/ProjectGallery.tsx`
```typescript
const images = category ? getImagesFromCategory(category) : [];
```

**Resolution Process**:
1. `getImagesFromCategory('belegg')`
2. → Uses `FALLBACK_IMAGES['belegg']` array
3. → Returns array of image objects with full paths
4. → Each path goes through `encodeImagePath()` for rendering

**Coupling**: Category-based with fallback arrays
**Risk**: Low - Flexible with fallback mechanism

## Critical Dependency Points

### 1. Path Encoding Function

**Location**: `src/lib/utils/paths.ts`
**Function**: `encodeImagePath(path: string)`

**Dependencies**: 
- ALL image rendering components
- Hero, ProjectCard, ProjectGallery, Services, About

**Failure Impact**: Complete image loading failure
**Coupling Strength**: Critical - Single point of failure

### 2. Image Configuration Module

**Location**: `src/lib/config/images.ts`

**Exports Used**:
- `IMAGE_PATHS` - Used by 8+ components
- `FEATURED_IMAGES` - Used by project/service systems
- `getFeaturedImagePath()` - Used by dynamic image loading
- `getProjectFeaturedImage()` - Used by project data
- `getServiceFeaturedImage()` - Used by service data

**Failure Impact**: Dynamic image loading failure
**Coupling Strength**: High - Central dependency

### 3. Vite Build Configuration

**Location**: `vite.config.ts`
**Setting**: `publicDir: "public"`

**Dependencies**: ALL static assets
**Failure Impact**: No images in build output
**Coupling Strength**: Critical - Build-time dependency

## Refactoring Opportunities

### 🎯 High Priority - Reduce Strong Coupling

#### 1. Centralize Hero Images
**Current**: Hardcoded in multiple locations
**Proposed**: Move all hero images to `IMAGE_PATHS.hero`

```typescript
// Instead of hardcoded strings
<Hero backgroundImage="/images/hero/hero-home-main.webp" />

// Use centralized configuration
<Hero backgroundImage={IMAGE_PATHS.hero.main} />
```

#### 2. Centralize Team Images
**Current**: Hardcoded in `CONTACT_INFO.team`
**Proposed**: Reference `IMAGE_PATHS.team`

```typescript
// Instead of hardcoded paths
team: [
  { image: '/images/team/ringerikelandskap-kim.webp' }
]

// Use centralized configuration
team: [
  { image: IMAGE_PATHS.team.kim }
]
```

#### 3. Remove Hardcoded Company Image
**Current**: Direct string in AboutPage
**Proposed**: Add to `IMAGE_PATHS.team.firma`

### 🎯 Medium Priority - Improve Flexibility

#### 1. Dynamic Featured Image Discovery
**Current**: Hardcoded `FEATURED_IMAGES` filenames
**Proposed**: Runtime directory scanning or configuration file

#### 2. Image Validation System
**Current**: No validation of image existence
**Proposed**: Build-time validation of all referenced images

#### 3. Image Optimization Pipeline
**Current**: No image processing
**Proposed**: Add Vite plugins for automatic WebP conversion and optimization

### 🎯 Low Priority - Architecture Improvements

#### 1. Image Service Layer
**Proposed**: Abstract all image operations behind service interface

```typescript
interface ImageService {
  getHeroImage(page: string): string;
  getTeamImage(memberId: string): string;
  getCategoryImages(category: string): ImageFile[];
  getFeaturedImage(category: string): string;
}
```

#### 2. Type-Safe Image References
**Proposed**: Generate TypeScript types from actual image files

## Risk Assessment

### High Risk Areas
1. **Featured Images**: Geocoordinate filenames are fragile
2. **Hero Images**: Multiple hardcoded references
3. **Path Encoding**: Single point of failure for all images

### Medium Risk Areas
1. **Team Images**: Centralized but hardcoded
2. **Build Process**: No validation of image references

### Low Risk Areas
1. **Dynamic Images**: Well-abstracted through functions
2. **Gallery System**: Flexible with fallback mechanisms

## Recommendations

### Immediate Actions
1. ✅ **Centralize all hardcoded hero images** in `IMAGE_PATHS`
2. ✅ **Add build-time image validation** to catch missing references
3. ✅ **Create image service abstraction** for better testability

### Long-term Improvements
1. 🔄 **Implement dynamic image discovery** to reduce hardcoded filenames
2. 🔄 **Add image optimization pipeline** for better performance
3. 🔄 **Create type-safe image reference system** for better DX

The current system shows excellent architectural thinking with centralized configuration and dynamic resolution, but has room for improvement in reducing hardcoded references and adding validation.
