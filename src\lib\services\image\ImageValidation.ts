/**
 * Image Validation Service
 * 
 * Provides comprehensive validation, error handling, and monitoring
 * for the dynamic image loading system.
 */

import { imageCollections } from '@/lib/assets/imageLoader';
import { IMAGE_CATEGORIES } from '@/lib/utils/images';

export interface ImageValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  suggestions: string[];
}

export interface ImageHealthReport {
  totalImages: number;
  validImages: number;
  missingImages: number;
  categories: {
    [category: string]: {
      imageCount: number;
      status: 'healthy' | 'warning' | 'error';
      issues: string[];
    };
  };
  recommendations: string[];
}

/**
 * Comprehensive image validation and health monitoring
 */
export class ImageValidationService {
  /**
   * Validate image URL and accessibility
   */
  static async validateImageUrl(url: string): Promise<ImageValidationResult> {
    const result: ImageValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      suggestions: []
    };

    // Basic URL validation
    if (!url || typeof url !== 'string') {
      result.isValid = false;
      result.errors.push('Invalid URL: URL must be a non-empty string');
      return result;
    }

    // Check URL format
    if (!url.startsWith('/') && !url.startsWith('http')) {
      result.warnings.push('URL should start with / for relative paths or http for absolute URLs');
    }

    // Check file extension
    const validExtensions = ['.webp', '.jpg', '.jpeg', '.png', '.gif'];
    const hasValidExtension = validExtensions.some(ext => url.toLowerCase().includes(ext));
    
    if (!hasValidExtension) {
      result.warnings.push('URL does not contain a recognized image file extension');
    }

    // Recommend WebP format
    if (!url.includes('.webp')) {
      result.suggestions.push('Consider using WebP format for better performance');
    }

    // Try to load the image to verify accessibility
    try {
      await this.testImageLoad(url);
    } catch (error) {
      result.isValid = false;
      result.errors.push(`Image failed to load: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return result;
  }

  /**
   * Test if an image can be loaded
   */
  private static testImageLoad(url: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      const timeout = setTimeout(() => {
        reject(new Error('Image load timeout'));
      }, 5000);

      img.onload = () => {
        clearTimeout(timeout);
        resolve();
      };

      img.onerror = () => {
        clearTimeout(timeout);
        reject(new Error('Image failed to load'));
      };

      img.src = url;
    });
  }

  /**
   * Validate image collections integrity
   */
  static validateImageCollections(): ImageValidationResult {
    const result: ImageValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      suggestions: []
    };

    // Check hero images
    const heroKeys = Object.keys(imageCollections.hero);
    if (heroKeys.length === 0) {
      result.isValid = false;
      result.errors.push('No hero images found in collection');
    } else {
      result.suggestions.push(`Found ${heroKeys.length} hero images`);
    }

    // Check team images
    const teamKeys = Object.keys(imageCollections.team);
    if (teamKeys.length === 0) {
      result.warnings.push('No team images found in collection');
    } else {
      result.suggestions.push(`Found ${teamKeys.length} team images`);
    }

    // Check category images
    const categoryKeys = Object.keys(imageCollections.categories);
    if (categoryKeys.length === 0) {
      result.isValid = false;
      result.errors.push('No category images found in collection');
    } else {
      result.suggestions.push(`Found ${categoryKeys.length} image categories`);
    }

    // Validate category names against expected categories
    const expectedCategories = Object.keys(IMAGE_CATEGORIES);
    const missingCategories = expectedCategories.filter(cat => !categoryKeys.includes(cat));
    
    if (missingCategories.length > 0) {
      result.warnings.push(`Missing categories: ${missingCategories.join(', ')}`);
    }

    const extraCategories = categoryKeys.filter(cat => !expectedCategories.includes(cat));
    if (extraCategories.length > 0) {
      result.suggestions.push(`Additional categories found: ${extraCategories.join(', ')}`);
    }

    return result;
  }

  /**
   * Generate comprehensive health report
   */
  static async generateHealthReport(): Promise<ImageHealthReport> {
    const report: ImageHealthReport = {
      totalImages: 0,
      validImages: 0,
      missingImages: 0,
      categories: {},
      recommendations: []
    };

    // Analyze hero images
    const heroImages = Object.values(imageCollections.hero);
    report.totalImages += heroImages.length;

    for (const url of heroImages) {
      try {
        await this.testImageLoad(url);
        report.validImages++;
      } catch {
        report.missingImages++;
      }
    }

    // Analyze team images
    const teamImages = Object.values(imageCollections.team);
    report.totalImages += teamImages.length;

    for (const url of teamImages) {
      try {
        await this.testImageLoad(url);
        report.validImages++;
      } catch {
        report.missingImages++;
      }
    }

    // Analyze category images
    for (const [category, loader] of Object.entries(imageCollections.categories)) {
      const categoryReport: {
        imageCount: number;
        status: 'healthy' | 'warning' | 'error';
        issues: string[];
      } = {
        imageCount: 0,
        status: 'healthy',
        issues: []
      };

      try {
        const images = await loader();
        const imageUrls = Object.values(images);
        categoryReport.imageCount = imageUrls.length;
        report.totalImages += imageUrls.length;

        // Test a sample of images from each category
        const sampleSize = Math.min(3, imageUrls.length);
        const sampleUrls = imageUrls.slice(0, sampleSize);

        let validCount = 0;
        for (const url of sampleUrls) {
          try {
            await this.testImageLoad(url as string);
            validCount++;
            report.validImages++;
          } catch {
            report.missingImages++;
            categoryReport.issues.push(`Failed to load sample image: ${url}`);
          }
        }

        // Estimate total valid images based on sample
        const estimatedValid = Math.round((validCount / sampleSize) * imageUrls.length);
        report.validImages += estimatedValid - validCount; // Add estimated remainder

        // Determine category status
        if (validCount === 0) {
          categoryReport.status = 'error';
          categoryReport.issues.push('No images could be loaded');
        } else if (validCount < sampleSize) {
          categoryReport.status = 'warning';
          categoryReport.issues.push('Some images failed to load');
        }

        if (categoryReport.imageCount === 0) {
          categoryReport.status = 'error';
          categoryReport.issues.push('No images found in category');
        } else if (categoryReport.imageCount < 3) {
          categoryReport.status = 'warning';
          categoryReport.issues.push('Very few images in category');
        }

      } catch (error) {
        categoryReport.status = 'error';
        categoryReport.issues.push(`Failed to load category: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }

      report.categories[category] = categoryReport;
    }

    // Generate recommendations
    report.recommendations = this.generateHealthRecommendations(report);

    return report;
  }

  /**
   * Generate health-based recommendations
   */
  private static generateHealthRecommendations(report: ImageHealthReport): string[] {
    const recommendations: string[] = [];

    // Overall health recommendations
    const healthRatio = report.totalImages > 0 ? report.validImages / report.totalImages : 0;
    
    if (healthRatio < 0.8) {
      recommendations.push('Image health is below 80% - investigate missing or broken images');
    }

    if (report.missingImages > 0) {
      recommendations.push(`${report.missingImages} images are missing or inaccessible`);
    }

    // Category-specific recommendations
    Object.entries(report.categories).forEach(([category, data]) => {
      if (data.status === 'error') {
        recommendations.push(`Category "${category}" has critical issues and needs attention`);
      } else if (data.status === 'warning') {
        recommendations.push(`Category "${category}" has warnings that should be addressed`);
      }

      if (data.imageCount === 0) {
        recommendations.push(`Add images to category "${category}"`);
      } else if (data.imageCount < 5) {
        recommendations.push(`Consider adding more images to category "${category}" (currently ${data.imageCount})`);
      }
    });

    // Performance recommendations
    if (report.totalImages > 100) {
      recommendations.push('Large number of images detected - consider implementing lazy loading');
    }

    if (Object.keys(report.categories).length > 10) {
      recommendations.push('Many image categories - consider organizing into subcategories');
    }

    return recommendations;
  }

  /**
   * Validate specific image category
   */
  static async validateCategory(category: string): Promise<ImageValidationResult> {
    const result: ImageValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      suggestions: []
    };

    const loader = imageCollections.categories[category];
    if (!loader) {
      result.isValid = false;
      result.errors.push(`Category "${category}" not found in image collections`);
      return result;
    }

    try {
      const images = await loader();
      const imageUrls = Object.values(images);

      if (imageUrls.length === 0) {
        result.isValid = false;
        result.errors.push(`No images found in category "${category}"`);
      } else {
        result.suggestions.push(`Found ${imageUrls.length} images in category "${category}"`);

        // Test first few images
        const testUrls = imageUrls.slice(0, 3);
        for (const url of testUrls) {
          try {
            await this.testImageLoad(url as string);
          } catch (error) {
            result.warnings.push(`Failed to load image: ${url}`);
          }
        }
      }

    } catch (error) {
      result.isValid = false;
      result.errors.push(`Failed to load category "${category}": ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return result;
  }

  /**
   * Get validation summary for monitoring
   */
  static async getValidationSummary() {
    const collectionValidation = this.validateImageCollections();
    const healthReport = await this.generateHealthReport();

    return {
      isHealthy: collectionValidation.isValid && healthReport.validImages > 0,
      totalImages: healthReport.totalImages,
      validImages: healthReport.validImages,
      missingImages: healthReport.missingImages,
      healthRatio: healthReport.totalImages > 0 ? healthReport.validImages / healthReport.totalImages : 0,
      criticalIssues: collectionValidation.errors.length,
      warnings: collectionValidation.warnings.length,
      recommendations: healthReport.recommendations.slice(0, 5) // Top 5 recommendations
    };
  }

  /**
   * Get fallback image for error cases
   */
  static getFallbackImage(context?: string): string {
    // Return appropriate fallback based on context
    switch (context) {
      case 'hero':
        return '/images/hero/hero-home-main.webp';
      case 'team':
        return '/images/team/ringerikelandskap-firma.webp';
      case 'category':
      default:
        return '/images/categorized/belegg/IMG_3037_60.181492_10.274272.webp';
    }
  }
}
