/**
 * Image Validation Service
 * 
 * Provides comprehensive validation, error handling, and monitoring
 * for the dynamic image loading system.
 */

import { imageCollections } from '@/lib/assets/imageLoader';
import { IMAGE_CATEGORIES } from '@/lib/utils/images';

export interface ImageValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  suggestions: string[];
}

export interface ImageHealthReport {
  totalImages: number;
  validImages: number;
  missingImages: number;
  categories: {
    [category: string]: {
      imageCount: number;
      status: 'healthy' | 'warning' | 'error';
      issues: string[];
    };
  };
  recommendations: string[];
}

/**
 * Image Validation Service for comprehensive error handling
 */
export class ImageValidationService {
  private static readonly SUPPORTED_FORMATS = ['webp', 'jpg', 'jpeg', 'png'];
  private static readonly MIN_IMAGES_PER_CATEGORY = 1;
  private static readonly RECOMMENDED_IMAGES_PER_CATEGORY = 3;

  /**
   * Validate a single image URL
   */
  static async validateImageUrl(url: string): Promise<ImageValidationResult> {
    const result: ImageValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      suggestions: []
    };

    // Check URL format
    if (!url || typeof url !== 'string') {
      result.isValid = false;
      result.errors.push('Invalid URL: URL must be a non-empty string');
      return result;
    }

    // Check file extension
    const extension = url.split('.').pop()?.toLowerCase();
    if (!extension || !this.SUPPORTED_FORMATS.includes(extension)) {
      result.isValid = false;
      result.errors.push(`Unsupported format: ${extension}. Supported: ${this.SUPPORTED_FORMATS.join(', ')}`);
    }

    // Check if URL is accessible (in browser environment)
    if (typeof window !== 'undefined') {
      try {
        await this.checkImageAccessibility(url);
      } catch (error) {
        result.isValid = false;
        result.errors.push(`Image not accessible: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    // Performance suggestions
    if (extension !== 'webp') {
      result.suggestions.push('Consider using WebP format for better performance');
    }

    return result;
  }

  /**
   * Check if an image is accessible
   */
  private static checkImageAccessibility(url: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      
      img.onload = () => resolve();
      img.onerror = () => reject(new Error('Failed to load image'));
      
      // Set timeout for loading
      setTimeout(() => {
        reject(new Error('Image loading timeout'));
      }, 5000);
      
      img.src = url;
    });
  }

  /**
   * Validate all images in a category
   */
  static async validateCategory(category: string): Promise<ImageValidationResult> {
    const result: ImageValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      suggestions: []
    };

    // Check if category exists
    if (!imageCollections.categories[category]) {
      result.isValid = false;
      result.errors.push(`Category '${category}' not found`);
      result.suggestions.push(`Available categories: ${Object.keys(imageCollections.categories).join(', ')}`);
      return result;
    }

    try {
      // Load category images
      const categoryLoader = imageCollections.categories[category];
      const images = await categoryLoader();

      // Check minimum images
      if (images.length < this.MIN_IMAGES_PER_CATEGORY) {
        result.isValid = false;
        result.errors.push(`Category '${category}' has insufficient images (${images.length} < ${this.MIN_IMAGES_PER_CATEGORY})`);
      }

      // Check recommended images
      if (images.length < this.RECOMMENDED_IMAGES_PER_CATEGORY) {
        result.warnings.push(`Category '${category}' has fewer than recommended images (${images.length} < ${this.RECOMMENDED_IMAGES_PER_CATEGORY})`);
      }

      // Validate each image
      for (const imageUrl of images) {
        const imageValidation = await this.validateImageUrl(imageUrl);
        if (!imageValidation.isValid) {
          result.isValid = false;
          result.errors.push(`Invalid image in '${category}': ${imageValidation.errors.join(', ')}`);
        }
        result.warnings.push(...imageValidation.warnings);
        result.suggestions.push(...imageValidation.suggestions);
      }

    } catch (error) {
      result.isValid = false;
      result.errors.push(`Failed to load category '${category}': ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return result;
  }

  /**
   * Generate comprehensive health report for all images
   */
  static async generateHealthReport(): Promise<ImageHealthReport> {
    const report: ImageHealthReport = {
      totalImages: 0,
      validImages: 0,
      missingImages: 0,
      categories: {},
      recommendations: []
    };

    // Validate hero images
    const heroImageCount = Object.keys(imageCollections.hero).length;
    report.totalImages += heroImageCount;
    report.validImages += heroImageCount; // Hero images are eagerly loaded, so they're valid

    // Validate team images
    const teamImageCount = Object.keys(imageCollections.team).length;
    report.totalImages += teamImageCount;
    report.validImages += teamImageCount; // Team images are eagerly loaded, so they're valid

    // Validate category images
    for (const category of Object.keys(imageCollections.categories)) {
      const validation = await this.validateCategory(category);
      
      try {
        const categoryLoader = imageCollections.categories[category];
        const images = await categoryLoader();
        const imageCount = images.length;
        
        report.totalImages += imageCount;
        
        report.categories[category] = {
          imageCount,
          status: validation.isValid ? 'healthy' : (validation.warnings.length > 0 ? 'warning' : 'error'),
          issues: [...validation.errors, ...validation.warnings]
        };

        if (validation.isValid) {
          report.validImages += imageCount;
        } else {
          report.missingImages += imageCount;
        }

      } catch (error) {
        report.categories[category] = {
          imageCount: 0,
          status: 'error',
          issues: [`Failed to load: ${error instanceof Error ? error.message : 'Unknown error'}`]
        };
      }
    }

    // Generate recommendations
    this.generateRecommendations(report);

    return report;
  }

  /**
   * Generate recommendations based on health report
   */
  private static generateRecommendations(report: ImageHealthReport): void {
    // Check overall health
    const healthPercentage = (report.validImages / report.totalImages) * 100;
    
    if (healthPercentage < 90) {
      report.recommendations.push('Image health is below 90%. Consider reviewing missing or invalid images.');
    }

    // Check category coverage
    const expectedCategories = Object.keys(IMAGE_CATEGORIES);
    const actualCategories = Object.keys(report.categories);
    const missingCategories = expectedCategories.filter(cat => !actualCategories.includes(cat));
    
    if (missingCategories.length > 0) {
      report.recommendations.push(`Missing image categories: ${missingCategories.join(', ')}`);
    }

    // Check for categories with few images
    Object.entries(report.categories).forEach(([category, info]) => {
      if (info.imageCount < this.RECOMMENDED_IMAGES_PER_CATEGORY) {
        report.recommendations.push(`Category '${category}' could benefit from more images (current: ${info.imageCount})`);
      }
    });

    // Performance recommendations
    if (report.totalImages > 100) {
      report.recommendations.push('Consider implementing image lazy loading for better performance');
    }
  }

  /**
   * Validate image collections integrity
   */
  static validateCollectionsIntegrity(): ImageValidationResult {
    const result: ImageValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      suggestions: []
    };

    // Check if collections are properly initialized
    if (!imageCollections) {
      result.isValid = false;
      result.errors.push('Image collections not initialized');
      return result;
    }

    // Check hero images
    if (!imageCollections.hero || Object.keys(imageCollections.hero).length === 0) {
      result.isValid = false;
      result.errors.push('No hero images found');
    }

    // Check team images
    if (!imageCollections.team || Object.keys(imageCollections.team).length === 0) {
      result.warnings.push('No team images found');
    }

    // Check categories
    if (!imageCollections.categories || Object.keys(imageCollections.categories).length === 0) {
      result.isValid = false;
      result.errors.push('No category images found');
    }

    return result;
  }

  /**
   * Get fallback image for any context
   */
  static getFallbackImage(context: 'hero' | 'team' | 'category' = 'category'): string {
    switch (context) {
      case 'hero':
        return '/images/hero/hero-home-main.webp';
      case 'team':
        return '/images/team/ringerikelandskap-firma.webp';
      case 'category':
      default:
        return '/images/hero/hero-home-main.webp';
    }
  }
}

export default ImageValidationService;
