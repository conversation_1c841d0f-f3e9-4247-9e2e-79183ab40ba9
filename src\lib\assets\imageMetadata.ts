/**
 * Image Metadata Generation and Management
 * 
 * This module provides utilities for generating SEO-friendly metadata
 * from image filenames and categories, including geocoordinate extraction
 * and contextual alt-text generation.
 */

/**
 * Interface for image metadata
 */
export interface ImageMetadata {
  alt: string;
  title?: string;
  description?: string;
  coordinates?: {
    lat: number;
    lng: number;
  };
  category?: string;
  filename?: string;
}

/**
 * Category display names in Norwegian
 */
const CATEGORY_DISPLAY_NAMES: Record<string, string> = {
  'belegg': 'Belegningsstein',
  'stål': 'Cortenstål',
  'støttemur': 'Støttemur',
  'trapp-repo': 'Trapper og Repoer',
  'kantstein': 'Kantstein',
  'platting': 'Platting',
  'hekk': 'Hekk og Beplantning',
  'ferdigplen': 'Ferdigplen'
};

/**
 * Hero image metadata mapping
 */
const HERO_IMAGE_METADATA: Record<string, ImageMetadata> = {
  'home-main': {
    alt: 'Landskapsprosjekt i Ringerike-området',
    title: 'Hovedbilde for hjemmeside',
    description: 'Profesjonell anleggsgartnertjeneste i Ringerike og Hole'
  },
  'about-ringerike': {
    alt: 'Ringerike Landskap - Om oss',
    title: 'Om Ringerike Landskap',
    description: 'Anleggsgartner og maskinentreprenør i Ringerike-regionen'
  },
  'services-granite': {
    alt: 'Våre tjenester - Granittarbeid',
    title: 'Tjenester - Granitt og steinarbeid',
    description: 'Profesjonelle anleggsgartnertjenester'
  },
  'services-grass': {
    alt: 'Våre tjenester - Plenarbeid',
    title: 'Tjenester - Plen og beplantning',
    description: 'Ferdigplen og beplantning'
  },
  'projects-grass': {
    alt: 'Våre prosjekter - Plenprosjekt',
    title: 'Prosjekter - Plen og hagearbeid',
    description: 'Gjennomførte prosjekter i Ringerike-området'
  },
  'projects-showcase': {
    alt: 'Våre prosjekter - Prosjektoversikt',
    title: 'Prosjekter - Showcase',
    description: 'Utvalgte prosjekter fra Ringerike Landskap'
  },
  'testimonials-cortensteel': {
    alt: 'Kundehistorier - Cortenstål prosjekt',
    title: 'Kundehistorier - Cortenstål',
    description: 'Fornøyde kunder og deres prosjekter'
  },
  'contact-illustrative': {
    alt: 'Kontakt oss - Ringerike Landskap',
    title: 'Kontakt - Ta kontakt for befaring',
    description: 'Kontakt Ringerike Landskap for gratis befaring'
  }
};

/**
 * Team member metadata mapping
 */
const TEAM_IMAGE_METADATA: Record<string, ImageMetadata> = {
  'kim': {
    alt: 'Kim Tuvsjøen - Anleggsgartner og medgründer',
    title: 'Kim Tuvsjøen',
    description: 'Medgründer med omfattende erfaring innen anleggsgartnerfaget'
  },
  'jan': {
    alt: 'Jan Iversen - Anleggsgartner og medgründer',
    title: 'Jan Iversen',
    description: 'Medgründer med spesialkompetanse på sveising og cortenstål'
  },
  'firma': {
    alt: 'Ringerike Landskap - Firmalogo og kontor',
    title: 'Ringerike Landskap AS',
    description: 'Anleggsgartner og maskinentreprenør i Ringerike'
  }
};

/**
 * Extract geocoordinates from filename
 * Handles filenames like: IMG_3037_60.181492_10.274272.webp
 * 
 * @param filename - Image filename
 * @returns Coordinates object or null if not found
 */
export const extractGeoCoordinates = (filename: string): { lat: number; lng: number } | null => {
  const geoMatch = filename.match(/_(\d+\.\d+)_(\d+\.\d+)\./);
  if (geoMatch) {
    return {
      lat: parseFloat(geoMatch[1]),
      lng: parseFloat(geoMatch[2])
    };
  }
  return null;
};

/**
 * Generate metadata for category images
 * 
 * @param filename - Image filename
 * @param category - Image category
 * @returns Generated metadata object
 */
export const generateCategoryImageMetadata = (filename: string, category: string): ImageMetadata => {
  const coordinates = extractGeoCoordinates(filename);
  const categoryName = CATEGORY_DISPLAY_NAMES[category] || category;
  
  const metadata: ImageMetadata = {
    alt: `${categoryName} prosjekt${coordinates ? ` (${coordinates.lat}, ${coordinates.lng})` : ''}`,
    title: `Profesjonell ${categoryName.toLowerCase()} utførelse`,
    description: `Kvalitetsarbeid innen ${categoryName.toLowerCase()} av Ringerike Landskap`,
    category,
    filename
  };
  
  if (coordinates) {
    metadata.coordinates = coordinates;
  }
  
  return metadata;
};

/**
 * Get hero image metadata
 * 
 * @param key - Hero image key
 * @returns Metadata object or default metadata
 */
export const getHeroImageMetadata = (key: string): ImageMetadata => {
  return HERO_IMAGE_METADATA[key] || {
    alt: 'Ringerike Landskap - Landskapsprosjekt',
    title: 'Ringerike Landskap',
    description: 'Profesjonell anleggsgartner i Ringerike-regionen'
  };
};

/**
 * Get team image metadata
 * 
 * @param memberId - Team member ID
 * @returns Metadata object or default metadata
 */
export const getTeamImageMetadata = (memberId: string): ImageMetadata => {
  return TEAM_IMAGE_METADATA[memberId] || {
    alt: 'Ringerike Landskap - Teammedlem',
    title: 'Ringerike Landskap Team',
    description: 'Erfaren anleggsgartner'
  };
};

/**
 * Generate comprehensive metadata for any image
 * 
 * @param imagePath - Full image path
 * @param context - Additional context (category, type, etc.)
 * @returns Complete metadata object
 */
export const generateImageMetadata = (imagePath: string, context?: {
  category?: string;
  type?: 'hero' | 'team' | 'category';
  key?: string;
}): ImageMetadata => {
  // Extract filename from path
  const filename = imagePath.split('/').pop() || '';
  
  if (context?.type === 'hero' && context.key) {
    return getHeroImageMetadata(context.key);
  }
  
  if (context?.type === 'team' && context.key) {
    return getTeamImageMetadata(context.key);
  }
  
  if (context?.type === 'category' && context.category) {
    return generateCategoryImageMetadata(filename, context.category);
  }
  
  // Default metadata
  return {
    alt: 'Ringerike Landskap - Landskapsprosjekt',
    title: 'Ringerike Landskap',
    description: 'Profesjonell anleggsgartner i Ringerike-regionen',
    filename
  };
};

/**
 * Validate image metadata completeness
 * 
 * @param metadata - Metadata object to validate
 * @returns Validation result with missing fields
 */
export const validateImageMetadata = (metadata: ImageMetadata): {
  isValid: boolean;
  missingFields: string[];
} => {
  const missingFields: string[] = [];
  
  if (!metadata.alt || metadata.alt.trim() === '') {
    missingFields.push('alt');
  }
  
  if (!metadata.title || metadata.title.trim() === '') {
    missingFields.push('title');
  }
  
  return {
    isValid: missingFields.length === 0,
    missingFields
  };
};

export default {
  extractGeoCoordinates,
  generateCategoryImageMetadata,
  getHeroImageMetadata,
  getTeamImageMetadata,
  generateImageMetadata,
  validateImageMetadata
};
