import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Alert<PERSON>riangle, RefreshCw } from 'lucide-react';
import { ImageValidationService } from '@/lib/services/ImageValidationService';

interface Props {
  children: ReactNode;
  fallbackImage?: string;
  showErrorDetails?: boolean;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
  retryCount: number;
}

/**
 * Error Boundary specifically for image-related components
 * Provides graceful fallbacks when image loading fails
 */
export class ImageErrorBoundary extends Component<Props, State> {
  private maxRetries = 3;

  constructor(props: Props) {
    super(props);
    this.state = { 
      hasError: false,
      retryCount: 0
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log error details
    console.error('Image Error Boundary caught an error:', error);
    console.error('Error Info:', errorInfo);

    // Update state with error details
    this.setState({
      error,
      errorInfo
    });

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Log to external service in production
    if (process.env.NODE_ENV === 'production') {
      // Example: logErrorToService(error, errorInfo);
    }
  }

  handleRetry = () => {
    if (this.state.retryCount < this.maxRetries) {
      this.setState(prevState => ({
        hasError: false,
        error: undefined,
        errorInfo: undefined,
        retryCount: prevState.retryCount + 1
      }));
    }
  };

  render() {
    if (this.state.hasError) {
      const { fallbackImage, showErrorDetails = false } = this.props;
      const { error, retryCount } = this.state;
      const canRetry = retryCount < this.maxRetries;

      return (
        <div className="flex flex-col items-center justify-center p-6 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
          {/* Error Icon */}
          <div className="flex items-center justify-center w-16 h-16 bg-red-100 rounded-full mb-4">
            <AlertTriangle className="h-8 w-8 text-red-600" />
          </div>

          {/* Error Message */}
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Bildefeil
          </h3>
          
          <p className="text-gray-600 text-center mb-4">
            Det oppstod en feil ved lasting av bilder. 
            {canRetry && ' Du kan prøve igjen.'}
          </p>

          {/* Fallback Image */}
          {fallbackImage && (
            <div className="mb-4">
              <img
                src={fallbackImage}
                alt="Fallback image"
                className="max-w-xs max-h-32 object-cover rounded"
                onError={(e) => {
                  // Hide fallback image if it also fails
                  (e.target as HTMLImageElement).style.display = 'none';
                }}
              />
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-2">
            {canRetry && (
              <button
                onClick={this.handleRetry}
                className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
              >
                <RefreshCw className="h-4 w-4" />
                Prøv igjen ({this.maxRetries - retryCount} forsøk igjen)
              </button>
            )}
            
            <button
              onClick={() => window.location.reload()}
              className="flex items-center gap-2 px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors"
            >
              <RefreshCw className="h-4 w-4" />
              Last siden på nytt
            </button>
          </div>

          {/* Error Details (Development) */}
          {showErrorDetails && error && process.env.NODE_ENV === 'development' && (
            <details className="mt-4 w-full">
              <summary className="cursor-pointer text-sm text-gray-500 hover:text-gray-700">
                Tekniske detaljer
              </summary>
              <div className="mt-2 p-3 bg-gray-100 rounded text-xs font-mono text-gray-700 overflow-auto max-h-32">
                <div><strong>Error:</strong> {error.message}</div>
                <div><strong>Stack:</strong> {error.stack}</div>
              </div>
            </details>
          )}
        </div>
      );
    }

    return this.props.children;
  }
}

/**
 * Higher-order component for wrapping image components with error boundary
 */
export function withImageErrorBoundary<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  fallbackImage?: string
) {
  const WithImageErrorBoundary = (props: P) => (
    <ImageErrorBoundary 
      fallbackImage={fallbackImage || ImageValidationService.getFallbackImage()}
      showErrorDetails={process.env.NODE_ENV === 'development'}
    >
      <WrappedComponent {...props} />
    </ImageErrorBoundary>
  );

  WithImageErrorBoundary.displayName = `withImageErrorBoundary(${WrappedComponent.displayName || WrappedComponent.name})`;
  
  return WithImageErrorBoundary;
}

/**
 * Hook for handling image loading errors
 */
export function useImageErrorHandler() {
  const [imageError, setImageError] = React.useState<string | null>(null);
  const [retryCount, setRetryCount] = React.useState(0);
  const maxRetries = 3;

  const handleImageError = React.useCallback((imageSrc: string) => {
    console.warn(`Image failed to load: ${imageSrc}`);
    setImageError(imageSrc);
  }, []);

  const retryImage = React.useCallback(() => {
    if (retryCount < maxRetries) {
      setImageError(null);
      setRetryCount(prev => prev + 1);
    }
  }, [retryCount, maxRetries]);

  const resetError = React.useCallback(() => {
    setImageError(null);
    setRetryCount(0);
  }, []);

  const getFallbackSrc = React.useCallback((context: 'hero' | 'team' | 'category' = 'category') => {
    return ImageValidationService.getFallbackImage(context);
  }, []);

  return {
    imageError,
    retryCount,
    maxRetries,
    canRetry: retryCount < maxRetries,
    handleImageError,
    retryImage,
    resetError,
    getFallbackSrc
  };
}

export default ImageErrorBoundary;
