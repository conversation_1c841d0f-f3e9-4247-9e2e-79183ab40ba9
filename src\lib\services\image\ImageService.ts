/**
 * ImageService - Type-safe dynamic image loading service
 * 
 * This service provides a clean API for accessing dynamically loaded images
 * while preserving all existing functionality and mapping relationships.
 * It maintains backward compatibility with the existing service-to-category
 * and project-to-category mapping systems.
 */

import { imageCollections } from '@/lib/assets/imageLoader';
import { encodeImagePath } from '@/lib/utils/paths';
// IMAGE_CATEGORIES import removed as it's not used in this service
import { ImageValidationService } from './ImageValidation';
import { ImageCacheService } from './ImageCache';
import { ImagePerformanceService } from './ImagePerformance';

// Import existing mapping configurations to preserve relationships
import {
  SERVICE_TO_IMAGE_CATEGORY,
  PROJECT_CATEGORY_TO_IMAGE_CATEGORY,
  FEATURED_IMAGES
} from '@/lib/config/images';

/**
 * Main ImageService class providing type-safe image access
 */
export class ImageService {
  /**
   * Get hero image by key
   * @param key - Hero image key (e.g., 'home-main', 'about-ringerike')
   * @returns Encoded image URL with fallback
   */
  static getHeroImage(key: string): string {
    try {
      // Validate input
      if (!key || typeof key !== 'string') {
        console.error('Invalid hero image key:', key);
        return this.getFallbackHero();
      }

      const image = imageCollections.hero[key];
      if (image) {
        return encodeImagePath(image);
      }

      console.warn(`Hero image not found: ${key}. Available keys:`, Object.keys(imageCollections.hero));

      // Try to suggest similar keys
      const availableKeys = Object.keys(imageCollections.hero);
      const similarKey = availableKeys.find(k => k.includes(key) || key.includes(k));
      
      if (similarKey) {
        console.info(`Did you mean: ${similarKey}?`);
        return encodeImagePath(imageCollections.hero[similarKey]);
      }

      return this.getFallbackHero();
    } catch (error) {
      console.error('Error getting hero image:', error);
      return this.getFallbackHero();
    }
  }

  /**
   * Get team member image by ID
   * @param memberId - Team member ID (e.g., 'jan', 'kim', 'firma')
   * @returns Encoded image URL with fallback
   */
  static getTeamImage(memberId: string): string {
    try {
      // Validate input
      if (!memberId || typeof memberId !== 'string') {
        console.error('Invalid team member ID:', memberId);
        return this.getFallbackTeam();
      }

      const image = imageCollections.team[memberId];
      if (image) {
        return encodeImagePath(image);
      }

      console.warn(`Team image not found: ${memberId}. Available keys:`, Object.keys(imageCollections.team));
      return this.getFallbackTeam();
    } catch (error) {
      console.error('Error getting team image:', error);
      return this.getFallbackTeam();
    }
  }

  /**
   * Get featured image for a category
   * @param category - Image category (e.g., 'belegg', 'stål')
   * @returns Promise resolving to encoded image URL
   */
  static async getCategoryFeatured(category: string): Promise<string> {
    try {
      // Validate input
      if (!category || typeof category !== 'string') {
        console.error('Invalid category:', category);
        return this.getFallbackCategory();
      }

      // Get the featured image filename for this category
      const featuredFilename = FEATURED_IMAGES[category as keyof typeof FEATURED_IMAGES];
      if (featuredFilename) {
        const imagePath = `/images/categorized/${category}/${featuredFilename}`;
        return encodeImagePath(imagePath);
      }

      // If no featured image, try to get the first available image
      const categoryImages = await this.getCategoryGalleryImages(category);
      if (categoryImages.length > 0) {
        return categoryImages[0];
      }

      console.warn(`No images found for category: ${category}`);
      return this.getFallbackCategory();
    } catch (error) {
      console.error('Error getting category featured image:', error);
      return this.getFallbackCategory();
    }
  }

  /**
   * Get service image using existing service-to-category mapping
   * @param serviceId - Service ID (e.g., 'belegningsstein', 'cortenstaal')
   * @returns Promise resolving to encoded image URL
   */
  static async getServiceImage(serviceId: string): Promise<string> {
    const category = SERVICE_TO_IMAGE_CATEGORY[serviceId as keyof typeof SERVICE_TO_IMAGE_CATEGORY];
    if (category) {
      return this.getCategoryFeatured(category);
    }
    
    console.warn(`Service ID not found in mapping: ${serviceId}. Available service IDs:`, Object.keys(SERVICE_TO_IMAGE_CATEGORY));
    return this.getFallbackCategory();
  }

  /**
   * Get project image using existing project-to-category mapping
   * @param projectCategory - Project category (e.g., 'Cortenstål', 'Belegningsstein')
   * @returns Promise resolving to encoded image URL
   */
  static async getProjectImage(projectCategory: string): Promise<string> {
    const category = PROJECT_CATEGORY_TO_IMAGE_CATEGORY[projectCategory as keyof typeof PROJECT_CATEGORY_TO_IMAGE_CATEGORY];
    if (category) {
      return this.getCategoryFeatured(category);
    }
    
    console.warn(`Project category not found in mapping: ${projectCategory}. Available categories:`, Object.keys(PROJECT_CATEGORY_TO_IMAGE_CATEGORY));
    return this.getFallbackCategory();
  }

  /**
   * Get all images from a category for gallery display
   * @param category - Image category
   * @returns Promise resolving to array of encoded image URLs
   */
  static async getCategoryGalleryImages(category: string): Promise<string[]> {
    try {
      // Validate input
      if (!category || typeof category !== 'string') {
        console.error('Invalid category for gallery:', category);
        return [];
      }

      const categoryLoader = imageCollections.categories[category];
      if (!categoryLoader) {
        console.warn(`Category not found: ${category}. Available categories:`, Object.keys(imageCollections.categories));
        return [];
      }

      // Load all images for this category
      const imageModules = await categoryLoader();
      const imageUrls = Object.values(imageModules).map(url => encodeImagePath(url as string));

      return imageUrls;
    } catch (error) {
      console.error(`Error loading gallery images for category ${category}:`, error);
      return [];
    }
  }

  /**
   * Get random images from a category
   * @param category - Image category
   * @param count - Number of images to return
   * @returns Promise resolving to array of encoded image URLs
   */
  static async getRandomCategoryImages(category: string, count: number = 6): Promise<string[]> {
    try {
      const allImages = await this.getCategoryGalleryImages(category);
      
      if (allImages.length === 0) {
        return [];
      }

      // Shuffle and take requested count
      const shuffled = [...allImages].sort(() => Math.random() - 0.5);
      return shuffled.slice(0, Math.min(count, allImages.length));
    } catch (error) {
      console.error(`Error getting random images for category ${category}:`, error);
      return [];
    }
  }

  /**
   * Fallback hero image
   */
  static getFallbackHero(): string {
    return encodeImagePath('/images/hero/hero-home-main.webp');
  }

  /**
   * Fallback team image
   */
  static getFallbackTeam(): string {
    return encodeImagePath('/images/team/ringerikelandskap-firma.webp');
  }

  /**
   * Fallback category image
   */
  static getFallbackCategory(): string {
    return encodeImagePath('/images/categorized/belegg/IMG_3037_60.181492_10.274272.webp');
  }

  /**
   * Preload critical images for performance
   */
  private static async preloadCriticalImages(): Promise<void> {
    try {
      const criticalImages = [
        this.getHeroImage('home-main'),
        this.getTeamImage('firma')
      ];

      await Promise.allSettled(
        criticalImages.map(url => ImageCacheService.preloadImage(url))
      );

      console.info('Critical images preloaded');
    } catch (error) {
      console.error('Error preloading critical images:', error);
    }
  }

  /**
   * Generate health report for monitoring
   * @returns Promise resolving to health report
   */
  static async generateHealthReport() {
    try {
      return await ImageValidationService.generateHealthReport();
    } catch (error) {
      console.error('Error generating health report:', error);
      return null;
    }
  }

  /**
   * Initialize performance optimization services
   */
  static initializePerformanceServices(): void {
    try {
      ImageCacheService.initialize();
      ImagePerformanceService.initialize();

      // Preload critical images
      this.preloadCriticalImages();

      console.info('Image performance services initialized');
    } catch (error) {
      console.error('Error initializing performance services:', error);
    }
  }

  /**
   * Get performance metrics
   */
  static getPerformanceMetrics() {
    try {
      return ImagePerformanceService.getMetrics();
    } catch (error) {
      console.error('Error getting performance metrics:', error);
      return null;
    }
  }

  /**
   * Get optimized image loading strategy
   */
  static getOptimizedLoadingStrategy(): any {
    try {
      // Collect all available image URLs
      const allUrls: string[] = [
        ...Object.values(imageCollections.hero),
        ...Object.values(imageCollections.team)
      ];

      // Add category images (first few from each category)
      Object.keys(imageCollections.categories).forEach(() => {
        // Note: This is async, but we'll handle it in the strategy
        allUrls.push(`category-placeholder`); // Placeholder for strategy calculation
      });

      return ImagePerformanceService.getOptimalLoadingStrategy(allUrls);
    } catch (error) {
      console.error('Error getting loading strategy:', error);
      return { eager: [], lazy: [], background: [], skip: [] };
    }
  }

  /**
   * Get performance report
   */
  static getPerformanceReport() {
    try {
      return ImagePerformanceService.getPerformanceSummary();
    } catch (error) {
      console.error('Error getting performance report:', error);
      return null;
    }
  }

  /**
   * Preload category images
   */
  static async preloadCategoryImages(category: string, limit: number = 5): Promise<void> {
    try {
      const images = await this.getCategoryGalleryImages(category);
      const imagesToPreload = images.slice(0, limit);
      await ImageCacheService.preloadCritical(imagesToPreload);
    } catch (error) {
      console.error(`Error preloading category images for ${category}:`, error);
    }
  }

  /**
   * Dispose performance services
   */
  static disposePerformanceServices(): void {
    try {
      ImagePerformanceService.resetMetrics();
      ImageCacheService.clearCache();
      console.info('Image performance services disposed');
    } catch (error) {
      console.error('Error disposing performance services:', error);
    }
  }
}

/**
 * Synchronous versions for backward compatibility
 * These maintain the existing API while the codebase migrates
 *
 * Note: These use the existing FEATURED_IMAGES mapping to provide
 * immediate synchronous access during the migration period.
 */
export class ImageServiceSync {
  /**
   * Synchronous service image getter (for immediate migration)
   * Uses the existing featured images mapping for immediate access
   */
  static getServiceImageSync(serviceId: string): string {
    const category = SERVICE_TO_IMAGE_CATEGORY[serviceId as keyof typeof SERVICE_TO_IMAGE_CATEGORY];
    if (category) {
      // Use the imported FEATURED_IMAGES mapping for sync access
      const filename = FEATURED_IMAGES[category as keyof typeof FEATURED_IMAGES];
      if (filename) {
        const imagePath = `/images/categorized/${category}/${filename}`;
        return encodeImagePath(imagePath);
      }
    }

    console.warn(`Service image not found for: ${serviceId}. Available service IDs:`, Object.keys(SERVICE_TO_IMAGE_CATEGORY));
    return ImageService.getFallbackCategory();
  }

  /**
   * Synchronous project image getter (for immediate migration)
   * Uses the existing featured images mapping for immediate access
   */
  static getProjectImageSync(projectCategory: string): string {
    const category = PROJECT_CATEGORY_TO_IMAGE_CATEGORY[projectCategory as keyof typeof PROJECT_CATEGORY_TO_IMAGE_CATEGORY];
    if (category) {
      // Use the imported FEATURED_IMAGES mapping for sync access
      const filename = FEATURED_IMAGES[category as keyof typeof FEATURED_IMAGES];
      if (filename) {
        const imagePath = `/images/categorized/${category}/${filename}`;
        return encodeImagePath(imagePath);
      }
    }

    console.warn(`Project image not found for: ${projectCategory}. Available categories:`, Object.keys(PROJECT_CATEGORY_TO_IMAGE_CATEGORY));
    return ImageService.getFallbackCategory();
  }

  /**
   * Synchronous team image getter (for immediate migration)
   * Uses the dynamic image collections for immediate access
   */
  static getTeamImage(memberId: string): string {
    const image = imageCollections.team[memberId];
    if (image) {
      return encodeImagePath(image);
    }

    console.warn(`Team image not found: ${memberId}. Available keys:`, Object.keys(imageCollections.team));
    return ImageService.getFallbackTeam();
  }
}

export default ImageService;
