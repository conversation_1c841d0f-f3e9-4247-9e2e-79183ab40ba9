/**
 * Image Services - Unified Export
 * 
 * This module provides a centralized export point for all image-related services,
 * creating a clean and organized API for image management throughout the application.
 */

// Main image service
export { ImageService, ImageServiceSync } from './ImageService';
export { default as ImageServiceDefault } from './ImageService';

// Performance and optimization services
export { ImageCacheService } from './ImageCache';
export { ImagePerformanceService } from './ImagePerformance';
export { ImageValidationService } from './ImageValidation';

// Type exports for external use
export type {
  CacheEntry,
  CacheStats,
  PreloadOptions
} from './ImageCache';

export type {
  PerformanceMetrics,
  LoadingStrategy,
  OptimizationRecommendations
} from './ImagePerformance';

export type {
  ImageValidationResult,
  ImageHealthReport
} from './ImageValidation';

// Import services for internal use
import { ImageCacheService as CacheService } from './ImageCache';
import { ImagePerformanceService as PerformanceService } from './ImagePerformance';
import { ImageValidationService as ValidationService } from './ImageValidation';
import { ImageService as MainService } from './ImageService';

/**
 * Convenience function to initialize all image services
 */
export const initializeImageServices = (): void => {
  try {
    CacheService.initialize();
    PerformanceService.initialize();
    console.info('✅ Image services initialized successfully');
  } catch (error) {
    console.error('❌ Failed to initialize image services:', error);
  }
};

/**
 * Get comprehensive image system status
 */
export const getImageSystemStatus = async () => {
  try {
    const [
      performanceMetrics,
      cacheStats,
      validationSummary
    ] = await Promise.all([
      PerformanceService.getPerformanceSummary(),
      CacheService.getStats(),
      ValidationService.getValidationSummary()
    ]);

    return {
      performance: performanceMetrics,
      cache: cacheStats,
      validation: validationSummary,
      overall: {
        status: validationSummary.isHealthy ? 'healthy' : 'warning',
        score: performanceMetrics.score,
        issues: [
          ...performanceMetrics.issues,
          ...(validationSummary.criticalIssues > 0 ? ['Critical validation issues detected'] : [])
        ],
        recommendations: [
          ...performanceMetrics.recommendations,
          ...validationSummary.recommendations
        ].slice(0, 5) // Top 5 recommendations
      }
    };
  } catch (error) {
    console.error('Error getting image system status:', error);
    return {
      performance: null,
      cache: null,
      validation: null,
      overall: {
        status: 'error',
        score: 0,
        issues: ['Failed to retrieve system status'],
        recommendations: ['Check image service configuration']
      }
    };
  }
};

/**
 * Emergency image system reset
 */
export const resetImageSystem = (): void => {
  try {
    CacheService.clearCache();
    console.warn('🔄 Image system reset - cache cleared');
  } catch (error) {
    console.error('❌ Failed to reset image system:', error);
  }
};

// Default export for convenience
export default MainService;
