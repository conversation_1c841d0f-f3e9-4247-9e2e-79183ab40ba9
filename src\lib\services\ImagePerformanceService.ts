/**
 * Image Performance Service
 * 
 * Monitors and optimizes image loading performance with intelligent
 * strategies for different network conditions and device capabilities.
 */

export interface PerformanceMetrics {
  totalImages: number;
  loadedImages: number;
  failedImages: number;
  averageLoadTime: number;
  totalLoadTime: number;
  cacheHitRate: number;
  networkSpeed: 'slow' | 'medium' | 'fast';
  deviceMemory?: number;
  connectionType?: string;
}

export interface LoadingStrategy {
  eager: string[];
  lazy: string[];
  background: string[];
  skip: string[];
}

export interface OptimizationRecommendations {
  reduceImageSizes: string[];
  enableLazyLoading: string[];
  preloadCritical: string[];
  useWebP: string[];
  implementCaching: boolean;
}

/**
 * Performance monitoring and optimization for image loading
 */
export class ImagePerformanceService {
  private static metrics: PerformanceMetrics = {
    totalImages: 0,
    loadedImages: 0,
    failedImages: 0,
    averageLoadTime: 0,
    totalLoadTime: 0,
    cacheHitRate: 0,
    networkSpeed: 'medium'
  };

  private static loadTimes: number[] = [];
  private static observer?: IntersectionObserver;
  private static networkInfo?: any;

  /**
   * Initialize performance monitoring
   */
  static initialize(): void {
    this.detectNetworkConditions();
    this.setupIntersectionObserver();
    this.monitorNetworkChanges();
  }

  /**
   * Detect network conditions and device capabilities
   */
  private static detectNetworkConditions(): void {
    if (typeof window === 'undefined') return;

    // Get network information
    this.networkInfo = (navigator as any).connection || 
                      (navigator as any).mozConnection || 
                      (navigator as any).webkitConnection;

    if (this.networkInfo) {
      this.updateNetworkSpeed();
      this.metrics.connectionType = this.networkInfo.effectiveType;
    }

    // Get device memory information
    if ('deviceMemory' in navigator) {
      this.metrics.deviceMemory = (navigator as any).deviceMemory;
    }
  }

  /**
   * Update network speed classification
   */
  private static updateNetworkSpeed(): void {
    if (!this.networkInfo) return;

    const { effectiveType, downlink } = this.networkInfo;
    
    if (effectiveType === '4g' && downlink > 10) {
      this.metrics.networkSpeed = 'fast';
    } else if (effectiveType === '3g' || (effectiveType === '4g' && downlink <= 10)) {
      this.metrics.networkSpeed = 'medium';
    } else {
      this.metrics.networkSpeed = 'slow';
    }
  }

  /**
   * Monitor network changes
   */
  private static monitorNetworkChanges(): void {
    if (this.networkInfo) {
      this.networkInfo.addEventListener('change', () => {
        this.updateNetworkSpeed();
        console.debug('Network conditions changed:', this.metrics.networkSpeed);
      });
    }
  }

  /**
   * Set up intersection observer for lazy loading optimization
   */
  private static setupIntersectionObserver(): void {
    if (typeof window === 'undefined' || !('IntersectionObserver' in window)) return;

    this.observer = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement;
            this.trackImageVisibility(img.src);
          }
        });
      },
      {
        rootMargin: '50px 0px', // Start loading 50px before image enters viewport
        threshold: 0.1
      }
    );
  }

  /**
   * Track image loading performance
   */
  static trackImageLoad(url: string, loadTime: number, fromCache: boolean = false): void {
    this.metrics.totalImages++;
    this.metrics.loadedImages++;
    this.loadTimes.push(loadTime);
    this.metrics.totalLoadTime += loadTime;
    this.metrics.averageLoadTime = this.metrics.totalLoadTime / this.metrics.loadedImages;

    if (fromCache) {
      this.updateCacheHitRate();
    }

    // Log slow loading images
    if (loadTime > 2000) {
      console.warn(`Slow image load detected: ${url} (${loadTime.toFixed(2)}ms)`);
    }
  }

  /**
   * Track image loading failure
   */
  static trackImageError(url: string, error: Error): void {
    this.metrics.totalImages++;
    this.metrics.failedImages++;
    
    console.error(`Image load failed: ${url}`, error);
  }

  /**
   * Track image visibility for lazy loading optimization
   */
  private static trackImageVisibility(url: string): void {
    // Track which images are actually viewed
    console.debug(`Image viewed: ${url}`);
  }

  /**
   * Update cache hit rate
   */
  private static updateCacheHitRate(): void {
    // This would be updated by the cache service
    // Placeholder for cache integration
  }

  /**
   * Get optimal loading strategy based on current conditions
   */
  static getOptimalLoadingStrategy(imageUrls: string[]): LoadingStrategy {
    const strategy: LoadingStrategy = {
      eager: [],
      lazy: [],
      background: [],
      skip: []
    };

    const { networkSpeed, deviceMemory } = this.metrics;
    const isLowEndDevice = deviceMemory && deviceMemory < 4;
    const isSlowNetwork = networkSpeed === 'slow';

    // Categorize images based on performance conditions
    imageUrls.forEach((url, index) => {
      const isHeroImage = url.includes('/hero/');
      const isTeamImage = url.includes('/team/');
      const isCritical = isHeroImage || (isTeamImage && index < 2);

      if (isCritical) {
        strategy.eager.push(url);
      } else if (isSlowNetwork || isLowEndDevice) {
        if (index < 10) {
          strategy.lazy.push(url);
        } else {
          strategy.skip.push(url);
        }
      } else {
        strategy.lazy.push(url);
        if (index < 20) {
          strategy.background.push(url);
        }
      }
    });

    return strategy;
  }

  /**
   * Generate performance recommendations
   */
  static generateRecommendations(imageUrls: string[]): OptimizationRecommendations {
    const recommendations: OptimizationRecommendations = {
      reduceImageSizes: [],
      enableLazyLoading: [],
      preloadCritical: [],
      useWebP: [],
      implementCaching: false
    };

    const { averageLoadTime, networkSpeed } = this.metrics;

    // Analyze performance issues
    if (averageLoadTime > 1000) {
      recommendations.implementCaching = true;
    }

    imageUrls.forEach(url => {
      // Check for non-WebP images
      if (!url.includes('.webp')) {
        recommendations.useWebP.push(url);
      }

      // Recommend preloading for critical images
      if (url.includes('/hero/') || url.includes('/team/')) {
        recommendations.preloadCritical.push(url);
      }

      // Recommend lazy loading for gallery images
      if (url.includes('/categorized/')) {
        recommendations.enableLazyLoading.push(url);
      }
    });

    // Network-specific recommendations
    if (networkSpeed === 'slow') {
      recommendations.reduceImageSizes = imageUrls.filter(url => 
        !url.includes('/hero/') // Don't reduce hero image quality
      );
    }

    return recommendations;
  }

  /**
   * Observe image for lazy loading
   */
  static observeImage(img: HTMLImageElement): void {
    if (this.observer) {
      this.observer.observe(img);
    }
  }

  /**
   * Unobserve image
   */
  static unobserveImage(img: HTMLImageElement): void {
    if (this.observer) {
      this.observer.unobserve(img);
    }
  }

  /**
   * Get current performance metrics
   */
  static getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  /**
   * Get performance summary
   */
  static getPerformanceSummary(): {
    score: number;
    issues: string[];
    recommendations: string[];
  } {
    const { averageLoadTime, failedImages, totalImages, networkSpeed } = this.metrics;
    const failureRate = totalImages > 0 ? (failedImages / totalImages) * 100 : 0;
    
    let score = 100;
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Deduct points for performance issues
    if (averageLoadTime > 2000) {
      score -= 30;
      issues.push(`Slow average load time: ${averageLoadTime.toFixed(0)}ms`);
      recommendations.push('Implement image caching and optimization');
    } else if (averageLoadTime > 1000) {
      score -= 15;
      issues.push(`Moderate load time: ${averageLoadTime.toFixed(0)}ms`);
      recommendations.push('Consider image preloading for critical assets');
    }

    if (failureRate > 5) {
      score -= 25;
      issues.push(`High failure rate: ${failureRate.toFixed(1)}%`);
      recommendations.push('Implement better error handling and fallbacks');
    } else if (failureRate > 1) {
      score -= 10;
      issues.push(`Some failed loads: ${failureRate.toFixed(1)}%`);
      recommendations.push('Add retry mechanisms for failed images');
    }

    if (networkSpeed === 'slow') {
      recommendations.push('Optimize for slow network conditions');
      recommendations.push('Implement progressive image loading');
    }

    return {
      score: Math.max(0, score),
      issues,
      recommendations
    };
  }

  /**
   * Reset metrics
   */
  static resetMetrics(): void {
    this.metrics = {
      totalImages: 0,
      loadedImages: 0,
      failedImages: 0,
      averageLoadTime: 0,
      totalLoadTime: 0,
      cacheHitRate: 0,
      networkSpeed: this.metrics.networkSpeed,
      deviceMemory: this.metrics.deviceMemory,
      connectionType: this.metrics.connectionType
    };
    this.loadTimes = [];
  }

  /**
   * Dispose of the service
   */
  static dispose(): void {
    if (this.observer) {
      this.observer.disconnect();
    }
  }
}

export default ImagePerformanceService;
