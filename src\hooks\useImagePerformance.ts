import { useEffect, useState, useCallback } from 'react';
import { ImageService } from '@/lib/services/ImageService';
import { ImageCacheService } from '@/lib/services/ImageCacheService';
import { ImagePerformanceService } from '@/lib/services/ImagePerformanceService';

export interface PerformanceState {
  isInitialized: boolean;
  metrics: any;
  cacheStats: any;
  recommendations: any;
  summary: any;
  loading: boolean;
  error: string | null;
}

/**
 * Hook for managing image performance optimization
 */
export const useImagePerformance = () => {
  const [state, setState] = useState<PerformanceState>({
    isInitialized: false,
    metrics: {},
    cacheStats: {},
    recommendations: {},
    summary: { score: 0, issues: [], recommendations: [] },
    loading: true,
    error: null
  });

  // Initialize performance services
  const initialize = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      
      // Initialize all performance services
      ImageService.initializePerformanceServices();
      
      // Get initial performance report
      const report = ImageService.getPerformanceReport();
      
      setState(prev => ({
        ...prev,
        isInitialized: true,
        loading: false,
        ...report
      }));
      
      console.info('Image performance services initialized successfully');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setState(prev => ({
        ...prev,
        loading: false,
        error: errorMessage
      }));
      console.error('Failed to initialize image performance services:', error);
    }
  }, []);

  // Refresh performance data
  const refresh = useCallback(() => {
    try {
      const report = ImageService.getPerformanceReport();
      setState(prev => ({
        ...prev,
        ...report,
        error: null
      }));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setState(prev => ({
        ...prev,
        error: errorMessage
      }));
    }
  }, []);

  // Preload category images
  const preloadCategory = useCallback(async (category: string, limit?: number) => {
    try {
      await ImageService.preloadCategoryImages(category, limit);
      refresh(); // Update stats after preloading
    } catch (error) {
      console.error(`Failed to preload category ${category}:`, error);
    }
  }, [refresh]);

  // Clear cache
  const clearCache = useCallback(() => {
    try {
      ImageCacheService.clearCache();
      refresh();
      console.info('Image cache cleared');
    } catch (error) {
      console.error('Failed to clear cache:', error);
    }
  }, [refresh]);

  // Reset performance metrics
  const resetMetrics = useCallback(() => {
    try {
      ImagePerformanceService.resetMetrics();
      refresh();
      console.info('Performance metrics reset');
    } catch (error) {
      console.error('Failed to reset metrics:', error);
    }
  }, [refresh]);

  // Get loading strategy
  const getLoadingStrategy = useCallback(() => {
    try {
      return ImageService.getOptimizedLoadingStrategy();
    } catch (error) {
      console.error('Failed to get loading strategy:', error);
      return { eager: [], lazy: [], background: [], skip: [] };
    }
  }, []);

  // Initialize on mount
  useEffect(() => {
    initialize();
    
    // Set up periodic refresh
    const interval = setInterval(refresh, 30000); // Refresh every 30 seconds
    
    return () => {
      clearInterval(interval);
      // Cleanup on unmount
      try {
        ImageService.disposePerformanceServices();
      } catch (error) {
        console.error('Error disposing performance services:', error);
      }
    };
  }, [initialize, refresh]);

  return {
    ...state,
    initialize,
    refresh,
    preloadCategory,
    clearCache,
    resetMetrics,
    getLoadingStrategy,
    
    // Computed values
    isHealthy: state.summary.score > 80,
    hasIssues: state.summary.issues.length > 0,
    cacheHitRate: state.cacheStats.hitRate || 0,
    averageLoadTime: state.metrics.averageLoadTime || 0,
    
    // Helper methods
    getCacheSize: () => state.cacheStats.totalEntries || 0,
    getFailureRate: () => {
      const { totalImages, failedImages } = state.metrics;
      return totalImages > 0 ? (failedImages / totalImages) * 100 : 0;
    }
  };
};

/**
 * Hook for monitoring specific image performance
 */
export const useImageMonitoring = (imageUrl: string) => {
  const [loadTime, setLoadTime] = useState<number>(0);
  const [isCached, setIsCached] = useState<boolean>(false);
  const [hasError, setHasError] = useState<boolean>(false);

  useEffect(() => {
    // Check if image is cached
    setIsCached(ImageCacheService.isCached(imageUrl));
    
    // Monitor image loading
    const startTime = performance.now();
    
    const img = new Image();
    img.onload = () => {
      const duration = performance.now() - startTime;
      setLoadTime(duration);
      setHasError(false);
      
      // Track performance
      ImagePerformanceService.trackImageLoad(imageUrl, duration, isCached);
    };
    
    img.onerror = () => {
      setHasError(true);
      ImagePerformanceService.trackImageError(imageUrl, new Error('Image load failed'));
    };
    
    img.src = imageUrl;
  }, [imageUrl, isCached]);

  return {
    loadTime,
    isCached,
    hasError,
    isOptimal: loadTime < 1000 && !hasError
  };
};

/**
 * Hook for adaptive loading based on network conditions
 */
export const useAdaptiveLoading = () => {
  const [networkSpeed, setNetworkSpeed] = useState<'slow' | 'medium' | 'fast'>('medium');
  const [shouldOptimize, setShouldOptimize] = useState<boolean>(false);

  useEffect(() => {
    const updateNetworkConditions = () => {
      const metrics = ImagePerformanceService.getMetrics();
      setNetworkSpeed(metrics.networkSpeed);
      setShouldOptimize(metrics.networkSpeed === 'slow' || (metrics.deviceMemory !== undefined && metrics.deviceMemory < 4));
    };

    updateNetworkConditions();
    
    // Listen for network changes
    if (typeof window !== 'undefined' && 'connection' in navigator) {
      const connection = (navigator as any).connection;
      connection.addEventListener('change', updateNetworkConditions);
      
      return () => {
        connection.removeEventListener('change', updateNetworkConditions);
      };
    }
  }, []);

  const getOptimalImageCount = useCallback((totalImages: number) => {
    switch (networkSpeed) {
      case 'slow':
        return Math.min(5, totalImages);
      case 'medium':
        return Math.min(15, totalImages);
      case 'fast':
      default:
        return totalImages;
    }
  }, [networkSpeed]);

  const shouldLazyLoad = useCallback((imageIndex: number) => {
    if (networkSpeed === 'slow') return imageIndex > 2;
    if (networkSpeed === 'medium') return imageIndex > 5;
    return imageIndex > 10;
  }, [networkSpeed]);

  return {
    networkSpeed,
    shouldOptimize,
    getOptimalImageCount,
    shouldLazyLoad,
    isSlowNetwork: networkSpeed === 'slow'
  };
};

export default useImagePerformance;
