# Image Management API Reference

## ImageService

Central service for all image operations with automatic discovery and optimization.

### Static Methods

#### `getHeroImage(key: string): string`
Get hero image by key with automatic fallback.

```typescript
const heroImage = ImageService.getHeroImage('home-main');
// Returns: "/images/hero/hero-home-main.webp"
```

**Parameters:**
- `key` - Hero image key (e.g., 'home-main', 'about-ringerike')

**Returns:** Encoded image URL with fallback handling

---

#### `getTeamImage(key: string): string`
Get team member photo by key with automatic fallback.

```typescript
const teamPhoto = ImageService.getTeamImage('jan');
// Returns: "/images/team/ringerikelandskap-jan.webp"
```

**Parameters:**
- `key` - Team member key (e.g., 'jan', 'kim', 'firma')

**Returns:** Encoded image URL with fallback handling

---

#### `getCategoryImages(category: string): Promise<string[]>`
Get all images for a category as URL array.

```typescript
const images = await ImageService.getCategoryImages('belegg');
// Returns: ["/images/categorized/belegg/IMG_123.webp", ...]
```

**Parameters:**
- `category` - Category key (e.g., 'belegg', 'hekk', 'plen')

**Returns:** Promise resolving to array of image URLs

---

#### `getCategoryGalleryImages(category: string): Promise<GeoImage[]>`
Get category images as GeoImage objects for gallery components.

```typescript
const galleryImages = await ImageService.getCategoryGalleryImages('stein');
// Returns: Array of GeoImage objects with metadata
```

**Parameters:**
- `category` - Category key

**Returns:** Promise resolving to array of GeoImage objects

---

#### `getRandomCategoryImages(category: string, count: number): Promise<string[]>`
Get random selection of images from a category.

```typescript
const randomImages = await ImageService.getRandomCategoryImages('terrasse', 6);
// Returns: Array of 6 random terrace project images
```

**Parameters:**
- `category` - Category key
- `count` - Number of images to return

**Returns:** Promise resolving to array of random image URLs

---

#### `getAllAvailableCategories(): string[]`
Get list of all available image categories.

```typescript
const categories = ImageService.getAllAvailableCategories();
// Returns: ['belegg', 'hekk', 'plen', 'stein', 'terrasse', 'tre']
```

**Returns:** Array of category keys

---

#### `validateCollections(): boolean`
Validate image collections integrity on startup.

```typescript
const isValid = ImageService.validateCollections();
// Returns: true if collections are valid
```

**Returns:** Boolean indicating validation success

---

#### `generateHealthReport(): Promise<ImageHealthReport | null>`
Generate comprehensive health report for monitoring.

```typescript
const report = await ImageService.generateHealthReport();
// Returns: Detailed health report with metrics and recommendations
```

**Returns:** Promise resolving to health report or null on error

---

## ImageCacheService

High-performance caching service with intelligent preloading strategies.

### Static Methods

#### `initialize(): void`
Initialize cache service with performance monitoring.

```typescript
ImageCacheService.initialize();
```

---

#### `preloadImage(url: string, options?: PreloadOptions): Promise<void>`
Preload single image with caching.

```typescript
await ImageCacheService.preloadImage('/images/hero/hero-home-main.webp', {
  priority: 'high',
  timeout: 5000
});
```

**Parameters:**
- `url` - Image URL to preload
- `options` - Optional preload configuration

---

#### `preloadCriticalImages(urls: string[]): Promise<void>`
Preload critical images with high priority.

```typescript
await ImageCacheService.preloadCriticalImages([
  '/images/hero/hero-home-main.webp',
  '/images/team/ringerikelandskap-firma.webp'
]);
```

**Parameters:**
- `urls` - Array of critical image URLs

---

#### `preloadInBackground(urls: string[]): void`
Preload images in background with low priority.

```typescript
ImageCacheService.preloadInBackground(categoryImages);
```

**Parameters:**
- `urls` - Array of image URLs to preload

---

#### `isCached(url: string): boolean`
Check if image is cached and valid.

```typescript
const cached = ImageCacheService.isCached('/images/hero/hero-home-main.webp');
```

**Parameters:**
- `url` - Image URL to check

**Returns:** Boolean indicating cache status

---

#### `getCacheStats(): CacheStats`
Get comprehensive cache statistics.

```typescript
const stats = ImageCacheService.getCacheStats();
// Returns: { totalEntries, totalSize, hitRate, averageLoadTime, ... }
```

**Returns:** Cache statistics object

---

#### `clearCache(): void`
Clear all cached images.

```typescript
ImageCacheService.clearCache();
```

---

## ImagePerformanceService

Performance monitoring and optimization service.

### Static Methods

#### `initialize(): void`
Initialize performance monitoring with network detection.

```typescript
ImagePerformanceService.initialize();
```

---

#### `trackImageLoad(url: string, loadTime: number, fromCache?: boolean): void`
Track image loading performance.

```typescript
ImagePerformanceService.trackImageLoad('/images/hero/hero-home-main.webp', 1250, false);
```

**Parameters:**
- `url` - Image URL
- `loadTime` - Load time in milliseconds
- `fromCache` - Whether loaded from cache

---

#### `trackImageError(url: string, error: Error): void`
Track image loading failure.

```typescript
ImagePerformanceService.trackImageError('/images/missing.webp', new Error('404'));
```

**Parameters:**
- `url` - Failed image URL
- `error` - Error object

---

#### `getMetrics(): PerformanceMetrics`
Get current performance metrics.

```typescript
const metrics = ImagePerformanceService.getMetrics();
// Returns: { totalImages, loadedImages, averageLoadTime, networkSpeed, ... }
```

**Returns:** Performance metrics object

---

#### `getOptimalLoadingStrategy(imageUrls: string[]): LoadingStrategy`
Get optimal loading strategy based on current conditions.

```typescript
const strategy = ImagePerformanceService.getOptimalLoadingStrategy(allImages);
// Returns: { eager: [...], lazy: [...], background: [...], skip: [...] }
```

**Parameters:**
- `imageUrls` - Array of image URLs to categorize

**Returns:** Loading strategy object

---

#### `generateRecommendations(imageUrls: string[]): OptimizationRecommendations`
Generate performance optimization recommendations.

```typescript
const recommendations = ImagePerformanceService.generateRecommendations(allImages);
// Returns: { reduceImageSizes: [...], enableLazyLoading: [...], ... }
```

**Parameters:**
- `imageUrls` - Array of image URLs to analyze

**Returns:** Optimization recommendations object

---

#### `getPerformanceSummary(): { score: number; issues: string[]; recommendations: string[] }`
Get performance summary with score and recommendations.

```typescript
const summary = ImagePerformanceService.getPerformanceSummary();
// Returns: { score: 85, issues: [...], recommendations: [...] }
```

**Returns:** Performance summary object

---

## ImageValidationService

Comprehensive validation and health monitoring service.

### Static Methods

#### `validateImageUrl(url: string): Promise<ImageValidationResult>`
Validate single image URL.

```typescript
const result = await ImageValidationService.validateImageUrl('/images/hero/hero-home-main.webp');
// Returns: { isValid: true, errors: [], warnings: [], suggestions: [] }
```

**Parameters:**
- `url` - Image URL to validate

**Returns:** Promise resolving to validation result

---

#### `validateCategory(category: string): Promise<ImageValidationResult>`
Validate all images in a category.

```typescript
const result = await ImageValidationService.validateCategory('belegg');
```

**Parameters:**
- `category` - Category key to validate

**Returns:** Promise resolving to validation result

---

#### `generateHealthReport(): Promise<ImageHealthReport>`
Generate comprehensive health report.

```typescript
const report = await ImageValidationService.generateHealthReport();
// Returns: Detailed health report with metrics and recommendations
```

**Returns:** Promise resolving to health report

---

#### `validateCollectionsIntegrity(): ImageValidationResult`
Validate image collections integrity.

```typescript
const result = ImageValidationService.validateCollectionsIntegrity();
```

**Returns:** Validation result for collections

---

#### `getFallbackImage(context?: 'hero' | 'team' | 'category'): string`
Get appropriate fallback image for context.

```typescript
const fallback = ImageValidationService.getFallbackImage('hero');
```

**Parameters:**
- `context` - Image context for appropriate fallback

**Returns:** Fallback image URL

---

## React Components

### OptimizedImage

High-performance image component with caching and monitoring.

```typescript
interface OptimizedImageProps extends Omit<React.ImgHTMLAttributes<HTMLImageElement>, 'loading' | 'onError'> {
  src: string;
  alt: string;
  priority?: 'high' | 'low' | 'auto';
  loading?: 'eager' | 'lazy' | 'auto';
  fallbackSrc?: string;
  onLoadStart?: () => void;
  onLoadComplete?: (loadTime: number) => void;
  onError?: (error: Error) => void;
  enableCache?: boolean;
  enablePerformanceTracking?: boolean;
  placeholder?: React.ReactNode;
  errorFallback?: React.ReactNode;
}
```

**Usage:**
```typescript
<OptimizedImage
  src="/images/hero/hero-home-main.webp"
  alt="Hero image"
  priority="high"
  loading="eager"
  enableCache={true}
  enablePerformanceTracking={true}
  onLoadComplete={(time) => console.log(`Loaded in ${time}ms`)}
/>
```

---

### ImageErrorBoundary

Error boundary specifically for image components.

```typescript
interface Props {
  children: ReactNode;
  fallbackImage?: string;
  showErrorDetails?: boolean;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}
```

**Usage:**
```typescript
<ImageErrorBoundary fallbackImage="/images/fallback.webp">
  <img src="/images/might-fail.webp" alt="Might fail" />
</ImageErrorBoundary>
```

---

## React Hooks

### useImagePerformance

Hook for managing image performance optimization.

```typescript
const {
  isInitialized,
  metrics,
  cacheStats,
  summary,
  isHealthy,
  preloadCategory,
  clearCache,
  resetMetrics
} = useImagePerformance();
```

**Returns:**
- `isInitialized` - Whether services are initialized
- `metrics` - Current performance metrics
- `cacheStats` - Cache statistics
- `summary` - Performance summary with score
- `isHealthy` - Whether system is healthy (score > 80)
- `preloadCategory` - Function to preload category images
- `clearCache` - Function to clear image cache
- `resetMetrics` - Function to reset performance metrics

---

### useOptimizedImage

Hook for optimized image loading with performance tracking.

```typescript
const { imageState, loadTime, isLoading, isLoaded, hasError } = useOptimizedImage(
  '/images/hero/hero-home-main.webp',
  {
    enableCache: true,
    enablePerformanceTracking: true,
    priority: 'high'
  }
);
```

**Parameters:**
- `src` - Image URL
- `options` - Configuration options

**Returns:**
- `imageState` - Current loading state
- `loadTime` - Load time in milliseconds
- `isLoading` - Whether image is loading
- `isLoaded` - Whether image loaded successfully
- `hasError` - Whether loading failed

---

### useImageErrorHandler

Hook for handling image loading errors.

```typescript
const {
  imageError,
  retryCount,
  canRetry,
  handleImageError,
  retryImage,
  resetError,
  getFallbackSrc
} = useImageErrorHandler();
```

**Returns:**
- `imageError` - Current error URL
- `retryCount` - Number of retry attempts
- `canRetry` - Whether retry is possible
- `handleImageError` - Function to handle errors
- `retryImage` - Function to retry loading
- `resetError` - Function to reset error state
- `getFallbackSrc` - Function to get fallback image

---

### useAdaptiveLoading

Hook for adaptive loading based on network conditions.

```typescript
const {
  networkSpeed,
  shouldOptimize,
  getOptimalImageCount,
  shouldLazyLoad,
  isSlowNetwork
} = useAdaptiveLoading();
```

**Returns:**
- `networkSpeed` - Current network speed classification
- `shouldOptimize` - Whether to apply optimizations
- `getOptimalImageCount` - Function to get optimal image count
- `shouldLazyLoad` - Function to determine lazy loading
- `isSlowNetwork` - Whether network is slow

---

## Type Definitions

### GeoImage
```typescript
interface GeoImage {
  filename: string;
  path: string;
  category: string;
  coordinates?: { latitude: number; longitude: number };
  metadata?: {
    title?: string;
    description?: string;
    location?: string;
  };
}
```

### ImageValidationResult
```typescript
interface ImageValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  suggestions: string[];
}
```

### PerformanceMetrics
```typescript
interface PerformanceMetrics {
  totalImages: number;
  loadedImages: number;
  failedImages: number;
  averageLoadTime: number;
  totalLoadTime: number;
  cacheHitRate: number;
  networkSpeed: 'slow' | 'medium' | 'fast';
  deviceMemory?: number;
  connectionType?: string;
}
```

### CacheStats
```typescript
interface CacheStats {
  totalEntries: number;
  totalSize: number;
  hitRate: number;
  averageLoadTime: number;
  oldestEntry: number;
  newestEntry: number;
}
```
