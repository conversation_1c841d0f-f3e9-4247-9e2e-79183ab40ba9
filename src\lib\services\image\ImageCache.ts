/**
 * Image Cache Service
 * 
 * Provides intelligent caching, preloading, and performance optimization
 * for the dynamic image loading system.
 */

export interface CacheEntry {
  url: string;
  timestamp: number;
  size?: number;
  loadTime?: number;
  hitCount: number;
  lastAccessed: number;
}

export interface CacheStats {
  totalEntries: number;
  totalSize: number;
  hitRate: number;
  averageLoadTime: number;
  oldestEntry: number;
  newestEntry: number;
}

export interface PreloadOptions {
  priority?: 'high' | 'low';
  crossOrigin?: 'anonymous' | 'use-credentials';
  timeout?: number;
}

/**
 * High-performance image cache with intelligent preloading
 */
export class ImageCacheService {
  private static cache = new Map<string, CacheEntry>();
  private static preloadQueue = new Set<string>();
  private static loadingPromises = new Map<string, Promise<void>>();
  private static maxCacheSize = 100; // Maximum number of cached entries
  private static maxCacheAge = 30 * 60 * 1000; // 30 minutes in milliseconds
  private static performanceObserver?: PerformanceObserver;

  /**
   * Initialize the cache service with performance monitoring
   */
  static initialize(): void {
    // Set up performance monitoring for image loads
    if (typeof window !== 'undefined' && 'PerformanceObserver' in window) {
      this.performanceObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.name.includes('/images/')) {
            this.updateLoadTime(entry.name, entry.duration);
          }
        }
      });
      
      this.performanceObserver.observe({ entryTypes: ['resource'] });
    }

    // Clean up cache periodically
    setInterval(() => this.cleanup(), 5 * 60 * 1000); // Every 5 minutes
  }

  /**
   * Preload an image with caching
   */
  static async preloadImage(url: string, options: PreloadOptions = {}): Promise<void> {
    if (this.cache.has(url)) {
      this.updateHitCount(url);
      return Promise.resolve();
    }

    if (this.loadingPromises.has(url)) {
      return this.loadingPromises.get(url)!;
    }

    const loadPromise = this.loadImageWithCache(url, options);
    this.loadingPromises.set(url, loadPromise);

    try {
      await loadPromise;
    } finally {
      this.loadingPromises.delete(url);
    }
  }

  /**
   * Load image with caching and performance tracking
   */
  private static async loadImageWithCache(url: string, options: PreloadOptions): Promise<void> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      const startTime = performance.now();
      
      // Set up timeout
      const timeout = options.timeout || 10000;
      const timeoutId = setTimeout(() => {
        reject(new Error(`Image load timeout: ${url}`));
      }, timeout);

      img.onload = () => {
        clearTimeout(timeoutId);
        const loadTime = performance.now() - startTime;
        
        // Cache the successful load
        this.addToCache(url, {
          url,
          timestamp: Date.now(),
          loadTime,
          hitCount: 1,
          lastAccessed: Date.now(),
          size: this.estimateImageSize(img)
        });

        resolve();
      };

      img.onerror = () => {
        clearTimeout(timeoutId);
        reject(new Error(`Failed to load image: ${url}`));
      };

      if (options.crossOrigin) {
        img.crossOrigin = options.crossOrigin;
      }

      img.src = url;
    });
  }

  /**
   * Estimate image size for cache management
   */
  private static estimateImageSize(img: HTMLImageElement): number {
    // Rough estimation: width * height * 4 bytes per pixel (RGBA)
    return img.naturalWidth * img.naturalHeight * 4;
  }

  /**
   * Add entry to cache with size management
   */
  private static addToCache(url: string, entry: CacheEntry): void {
    // Remove oldest entries if cache is full
    if (this.cache.size >= this.maxCacheSize) {
      this.evictOldestEntries(Math.floor(this.maxCacheSize * 0.2)); // Remove 20%
    }

    this.cache.set(url, entry);
  }

  /**
   * Update hit count for cache entry
   */
  private static updateHitCount(url: string): void {
    const entry = this.cache.get(url);
    if (entry) {
      entry.hitCount++;
      entry.lastAccessed = Date.now();
    }
  }

  /**
   * Update load time for cache entry
   */
  private static updateLoadTime(url: string, loadTime: number): void {
    const entry = this.cache.get(url);
    if (entry) {
      entry.loadTime = loadTime;
    }
  }

  /**
   * Evict oldest entries from cache
   */
  private static evictOldestEntries(count: number): void {
    const entries = Array.from(this.cache.entries())
      .sort(([, a], [, b]) => a.lastAccessed - b.lastAccessed);

    for (let i = 0; i < count && i < entries.length; i++) {
      this.cache.delete(entries[i][0]);
    }
  }

  /**
   * Clean up expired cache entries
   */
  private static cleanup(): void {
    const now = Date.now();
    for (const [url, entry] of this.cache.entries()) {
      if (now - entry.timestamp > this.maxCacheAge) {
        this.cache.delete(url);
      }
    }
  }

  /**
   * Get cache statistics
   */
  static getStats(): CacheStats {
    const entries = Array.from(this.cache.values());
    const totalSize = entries.reduce((sum, entry) => sum + (entry.size || 0), 0);
    const totalHits = entries.reduce((sum, entry) => sum + entry.hitCount, 0);
    const totalRequests = entries.length;
    const loadTimes = entries.filter(e => e.loadTime).map(e => e.loadTime!);
    const averageLoadTime = loadTimes.length > 0 
      ? loadTimes.reduce((sum, time) => sum + time, 0) / loadTimes.length 
      : 0;

    const timestamps = entries.map(e => e.timestamp);
    const oldestEntry = timestamps.length > 0 ? Math.min(...timestamps) : 0;
    const newestEntry = timestamps.length > 0 ? Math.max(...timestamps) : 0;

    return {
      totalEntries: entries.length,
      totalSize,
      hitRate: totalRequests > 0 ? totalHits / totalRequests : 0,
      averageLoadTime,
      oldestEntry,
      newestEntry
    };
  }

  /**
   * Clear all cache entries
   */
  static clearCache(): void {
    this.cache.clear();
    this.preloadQueue.clear();
    this.loadingPromises.clear();
  }

  /**
   * Preload images in background with low priority
   */
  static preloadInBackground(urls: string[]): void {
    // Use requestIdleCallback for background preloading
    const preloadBatch = () => {
      const batchSize = 3; // Preload 3 images at a time
      const batch = urls.splice(0, batchSize);
      
      if (batch.length === 0) return;

      const promises = batch.map(url => 
        this.preloadImage(url, { priority: 'low', timeout: 15000 })
          .catch(error => console.debug(`Background preload failed: ${url}`, error))
      );

      Promise.allSettled(promises).then(() => {
        if (urls.length > 0) {
          // Schedule next batch
          if (typeof window !== 'undefined' && 'requestIdleCallback' in window) {
            window.requestIdleCallback(preloadBatch);
          } else {
            setTimeout(preloadBatch, 100);
          }
        }
      });
    };

    if (typeof window !== 'undefined' && 'requestIdleCallback' in window) {
      window.requestIdleCallback(preloadBatch);
    } else {
      setTimeout(preloadBatch, 0);
    }
  }

  /**
   * Check if image is cached
   */
  static isCached(url: string): boolean {
    const entry = this.cache.get(url);
    if (!entry) return false;

    // Check if entry is still valid
    if (Date.now() - entry.timestamp > this.maxCacheAge) {
      this.cache.delete(url);
      return false;
    }

    this.updateHitCount(url);
    return true;
  }

  /**
   * Get cache entry for URL
   */
  static getCacheEntry(url: string): CacheEntry | undefined {
    return this.cache.get(url);
  }

  /**
   * Preload critical images immediately
   */
  static async preloadCritical(urls: string[]): Promise<void> {
    await Promise.allSettled(
      urls.map(url => this.preloadImage(url, { priority: 'high', timeout: 5000 }))
    );
  }
}
